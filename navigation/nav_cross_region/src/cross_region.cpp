#include "cross_region.hpp"

#include "cross_region_config.hpp"
#include "imu_data_processor.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

using namespace mower_msgs::msg;

namespace fescue_iox
{

NavigationCrossRegionAlg::NavigationCrossRegionAlg(const CrossRegionAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("CrossRegion"))
{
    last_cooldown_time_ = std::chrono::steady_clock::now();
    straight_start_time_ = std::chrono::steady_clock::now();
    beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();

    last_distance_update_time_ = std::chrono::steady_clock::time_point{};
    last_grass_distance_update_time_ = std::chrono::steady_clock::time_point{};

    linear_start_time_ = std::chrono::steady_clock::now();
    last_imu_time_ = std::chrono::steady_clock::now();

    bias_samples_.clear();
    SetCrossRegionAlgParam(param);
    InitPublisher();

    // Initialize IMU data processor
    InitializeImuProcessor();
}

NavigationCrossRegionAlg::~NavigationCrossRegionAlg()
{
    PublishVelocity(0.0, 0.0, 1000); // Keep publishing for 1s
    ShutdownImuProcessor();
    LOG_WARN("NavigationCrossRegionAlg exit!");
}

void NavigationCrossRegionAlg::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);
    motor_speed_data_ = motor_speed_data;

    GetVelocityFromMotorRPM(motor_speed_data.motor_speed_left,
                            motor_speed_data.motor_speed_right,
                            wheel_radius_, wheel_base_, act_linear_, act_angular_);

    if (imu_processor_)
    {
        imu_processor_->SetMotorSpeedData(act_linear_, act_angular_);
    }
}

void NavigationCrossRegionAlg::DataConversion(MarkLocationResult &mark_loc_result)
{
    LOG_INFO_THROTTLE(5000, "[CrossRegion]  Coordinate transformation MarkLocation result: detect_status: {} mark_perception_status: {} mark_perception_direction: {} roi_confidence: {} "
                            "target_direction : {} markID : {} v_markID_dis.size : {} xyz({} {} {}) yaw({})",
                      mark_loc_result.detect_status, mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
                      mark_loc_result.roi_confidence, mark_loc_result.target_direction, mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
                      mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                      Radians2Degrees(mark_loc_result.xyzrpw.w));

    for (const auto &mark_id_distance : mark_loc_result.mark_id_distance)
    {
        LOG_INFO_THROTTLE(5000, "[CrossRegion] mark_id_distance beacon and distance: mark_id({}), distance({})",
                          mark_id_distance.mark_id, mark_id_distance.distance);
    }

    if (mark_loc_result.roi_confidence >= 60 && mark_loc_result.roi_confidence <= 100) // 60~100
    {
        mark_loc_result.roi_confidence = 1; // 1 Indicates in ROI area
    }
    else if (mark_loc_result.roi_confidence < 60 && mark_loc_result.roi_confidence >= 0) // 0~60
    {
        mark_loc_result.roi_confidence = 0; // 0 Indicates not in ROI area
    }
    else
    {
        mark_loc_result.roi_confidence = -1; //-1 Indicates detection failed
    }

    // Convert the coordinate system from mark as the right-hand coordinate system, camera relative to mark, to base_link relative to mark
    if (mark_loc_result.detect_status == 2)
    {
        // Input fixed coordinates of camera relative to base_link
        Pose_Mark camera_to_base_link = {camera_2_center_dis_, 0.0, 0.0, 0.0, 0.0, 0.0};

        // Input coordinates of camera relative to mark
        Pose_Mark camera_to_mark = {mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                                    mark_loc_result.xyzrpw.r, mark_loc_result.xyzrpw.p, mark_loc_result.xyzrpw.w};

        // Calculate coordinates of base_link relative to mark
        Pose_Mark base_link_to_mark = calculateBaseLinkRelativeToMark(camera_to_mark, camera_to_base_link);

        mark_loc_result.xyzrpw.x = base_link_to_mark.x;
        mark_loc_result.xyzrpw.y = base_link_to_mark.y;
        mark_loc_result.xyzrpw.z = base_link_to_mark.z;
        mark_loc_result.xyzrpw.r = base_link_to_mark.roll;
        mark_loc_result.xyzrpw.p = base_link_to_mark.pitch;
        mark_loc_result.xyzrpw.w = base_link_to_mark.yaw;

        LOG_INFO_THROTTLE(5000, "[CrossRegion]  Coordinate transformation MarkLocation result: detect_status({}) mark_perception_status({}) mark_perception_direction({}) "
                                "roi_confidence({}) target_direction({}) markID({}) v_markID_dis.size({}) xyz({} {} {}) yaw({})",
                          mark_loc_result.detect_status,
                          mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
                          mark_loc_result.roi_confidence, mark_loc_result.target_direction,
                          mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
                          mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                          Radians2Degrees(mark_loc_result.xyzrpw.w));
    }
}

void NavigationCrossRegionAlg::SetCrossRegionAlgParam(const CrossRegionAlgParam &param)
{
    cross_region_linear_ = param.cross_region_linear;
    cross_region_angular_ = param.cross_region_angular;
    max_distance_threshold_ = param.max_distance_threshold;
    min_distance_threshold_ = param.min_distance_threshold;
    cross_region_special_linear_ = param.cross_region_special_linear;
    cross_region_special_angular_ = param.cross_region_special_angular;
    dis_tolerance_ = param.dis_tolerance;
    cross_region_angle_compensation_ = param.cross_region_angle_compensation;
    channel_stop_pose_x_ = param.channel_stop_pose_x;
    grass_count_threshold_ = param.grass_count_threshold;
    edge_mode_direction_ = param.edge_mode_direction;
    channel_width_ = param.channel_width;
    camera_2_center_dis_ = param.camera_2_center_dis;
    adjust_mode_x_direction_threshold_ = param.adjust_mode_x_direction_threshold;
    mark_distance_threshold_ = param.mark_distance_threshold;
    perception_drive_cooldown_time_threshold_ = param.perception_drive_cooldown_time_threshold;
    cross_region_adjust_displace_ = param.cross_region_adjust_displace;
    channel_fixed_distance_ = param.channel_fixed_distance;
}

void NavigationCrossRegionAlg::GetCrossRegionAlgParam(CrossRegionAlgParam &param)
{
    param.cross_region_linear = cross_region_linear_;
    param.cross_region_angular = cross_region_angular_;
    param.max_distance_threshold = max_distance_threshold_;
    param.min_distance_threshold = min_distance_threshold_;
    param.cross_region_special_linear = cross_region_special_linear_;
    param.cross_region_special_angular = cross_region_special_angular_;
    param.dis_tolerance = dis_tolerance_;
    param.cross_region_angle_compensation = cross_region_angle_compensation_;
    param.channel_stop_pose_x = channel_stop_pose_x_;
    param.grass_count_threshold = grass_count_threshold_;
    param.edge_mode_direction = edge_mode_direction_;
    param.channel_width = channel_width_;
    param.camera_2_center_dis = camera_2_center_dis_;
    param.adjust_mode_x_direction_threshold = adjust_mode_x_direction_threshold_;
    param.mark_distance_threshold = mark_distance_threshold_;
    param.perception_drive_cooldown_time_threshold = perception_drive_cooldown_time_threshold_;
    param.cross_region_adjust_displace = cross_region_adjust_displace_;
    param.channel_fixed_distance = channel_fixed_distance_;
}

void NavigationCrossRegionAlg::SetMarkLocationResult(const MarkLocationResult &mark_loc_result)
{
    if (apply_coordinate_transform_)
    {
        std::lock_guard<std::mutex> lck(mark_loc_mutex_);
        mark_loc_result_ = mark_loc_result;
        mark_loc_result_law_ = mark_loc_result;
        DataConversion(mark_loc_result_);
    }
}

void NavigationCrossRegionAlg::SetRawPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &raw_fusion_result)
{
    std::lock_guard<std::mutex> lck(raw_fusion_mutex_);
    raw_fusion_result_ = raw_fusion_result;
}

void NavigationCrossRegionAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO_THROTTLE(1000, "NavigationCrossRegionAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        // Initialize data time information when starting
        {
            std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
            data_time_info_map_.clear();
            uint64_t now_timestamp = GetSteadyClockTimestampMs() * 1000;
            DataTimeInfo data_time_info;
            // Initialize receive timestamp to current time
            data_time_info.recv_timestamp = now_timestamp;
            // Initialize send timestamp to 0
            data_time_info.send_timestamp = 0;
            data_time_info.is_low_freq = false;
            data_time_info.low_freq_count = 0;
            data_time_info.is_timeout = false;
            data_time_info_map_["PerceptionFusion"] = data_time_info;
            data_time_info_map_["MarkLocation"] = data_time_info;
            data_time_info_map_["ImuData"] = data_time_info;
            data_time_info_map_["McuException"] = data_time_info;
            data_time_info_map_["MotorSpeed"] = data_time_info;
        }
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_ERROR("[NavigationCrossRegionAlg] Unknown state {}!", static_cast<int>(state));
    }
}

const char *NavigationCrossRegionAlg::GetVersion()
{
    return "V1.2.2";
}

void NavigationCrossRegionAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

void NavigationCrossRegionAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationCrossRegionAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0);
}

void NavigationCrossRegionAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationCrossRegionAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationCrossRegionAlg::ShowMowerRunningInfo(const MarkLocationResult &mark_loc_result)
{
    (void)mark_loc_result;
    // Print time difference
    LOG_WARN_THROTTLE(2000, "[CrossRegion] Perception-driven cooldown timer (seconds): ({})!", perception_drive_duration_.count());
}

CrossRegionAlgResult NavigationCrossRegionAlg::DoCrossRegion(PerceptionFusionResult &fusion_result,
                                                             MarkLocationResult &mark_loc_result,
                                                             ImuData &imu_data,
                                                             McuExceptionStatus &mcu_exception_status)
{
    (void)imu_data;

    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "[CrossRegion] DoCrossRegion() is PAUSE!");
        return CrossRegionAlgResult(false, CrossRegionStatus::InProgress);
    }

    // Perception-driven cooldown time
    ShowMowerRunningInfo(mark_loc_result);

    // Update data time information for all input sources
    SetPerceptionFusionResult(fusion_result);
    SetMarkLocationResultWithTimeInfo(mark_loc_result);
    SetImuDataWithTimeInfo(imu_data);
    SetMcuExceptionWithTimeInfo(mcu_exception_status);

    // Update MotorSpeed data time information
    {
        std::lock_guard<std::mutex> lock(motor_speed_mtx_);
        SetMotorSpeedDataWithTimeInfo(motor_speed_data_);
    }

    // Comprehensive input data validation
    if (CheckPerceptionFusionDataError(fusion_result))
    {
        LOG_ERROR_THROTTLE(1000, "[CrossRegion] PerceptionFusion data error detected, stopping motion");
        PublishZeroVelocity();
        return CrossRegionAlgResult(false, CrossRegionStatus::InProgress);
    }

    if (CheckMarkLocationDataError(mark_loc_result))
    {
        LOG_ERROR_THROTTLE(1000, "[CrossRegion] MarkLocation data error detected, stopping motion");
        PublishZeroVelocity();
        return CrossRegionAlgResult(false, CrossRegionStatus::InProgress);
    }

    if (CheckImuDataError())
    {
        LOG_ERROR_THROTTLE(1000, "[CrossRegion] IMU data error detected, stopping motion");
        PublishZeroVelocity();
        return CrossRegionAlgResult(false, CrossRegionStatus::InProgress);
    }

    if (CheckMcuExceptionDataError())
    {
        LOG_ERROR_THROTTLE(1000, "[CrossRegion] MCU exception data error detected, stopping motion");
        PublishZeroVelocity();
        return CrossRegionAlgResult(false, CrossRegionStatus::InProgress);
    }

    if (CheckMotorSpeedDataError())
    {
        LOG_ERROR_THROTTLE(1000, "[CrossRegion] MotorSpeed data error detected, stopping motion");
        PublishZeroVelocity();
        return CrossRegionAlgResult(false, CrossRegionStatus::InProgress);
    }

    // 检查初始条件
    if (!CheckInitialConditions(fusion_result))
    {
        LOG_ERROR_THROTTLE(1000, "[CrossRegion] Initial conditions check failed, skipping this cycle");

        PublishZeroVelocity();
        return CrossRegionAlgResult(false, CrossRegionStatus::InProgress);
    }

    // Handle different MCU exception states
    switch (mcu_exception_status)
    {
    case McuExceptionStatus::COLLISION:
    case McuExceptionStatus::LIFTING:
    {
        HandleMcuException();
        return CrossRegionAlgResult(false, CrossRegionStatus::InProgress);
    }
    case McuExceptionStatus::NORMAL:
    default:
    {
        // HandleNormalCrossRegionStates();
        return HandleNormalOperation(fusion_result, mark_loc_result);
    }
    }
}

bool NavigationCrossRegionAlg::CheckInitialConditions(const PerceptionFusionResult &fusion_result)
{
    // 获取占用栅格信息
    const auto &occupancy_grid = fusion_result.occupancy_grid;
    int height = occupancy_grid.height;
    int width = occupancy_grid.width;
    float resolution = occupancy_grid.resolution;

    // 检查占用栅格数据是否有效
    if (occupancy_grid.grid.empty() || height <= 0 || width <= 0 || resolution <= 0)
    {
        LOG_ERROR_THROTTLE(1000, "[CrossRegion] Invalid occupancy grid data!");
        return false;
    }

    return true;
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_1(MarkLocationResult &mark_loc_result)
{
    if (!phase_1_completed_ && !phase_2_completed_ && !phase_3_completed_)
    {
        FindBeaconsPhase_1(mark_loc_result);
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_2(MarkLocationResult &mark_loc_result,
                                                      float &x_first, float &y_first, float &yaw_first)
{
    if (phase_1_completed_ && !phase_2_completed_ && !phase_3_completed_)
    {
        EdgeFollowDisable();

        if (mark_loc_result.detect_status != 2 || mark_loc_result.mark_perception_direction != 0) // Beacon cannot calculate pose or the mower is not facing the beacon
        {
            LOG_INFO("[CrossRegion] [Phase 2] Beacon cannot calculate pose, start Phase 1, find beacon along the edge");
            phase_1_completed_ = false;
            PublishZeroVelocity();
        }
        else
        {
            LOG_INFO("[CrossRegion] [Phase 2] Get precise beacon QR code pose");
            GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_2_completed_);

            // // Within the confidence region
            // if (mark_loc_result.roi_confidence == 1)
            // {
            //     LOG_INFO("[CrossRegion] [Phase 2] Beacon appears in the confidence region");
            //     GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_2_completed_);
            // }
            // else // Outside the confidence region
            // {
            //     LOG_INFO("[CrossRegion] [Phase 2] Beacon appears outside the confidence region");
            //     CorrectionOutsideConfidence(mark_loc_result);
            // }
        }
    }
}

CrossRegionAlgResult NavigationCrossRegionAlg::DealCrossRegionPhase_3(MarkLocationResult &mark_loc_result,
                                                                      const float &x_first, const float &y_first, const float &yaw_first)
{
    (void)mark_loc_result;
    bool cross_region_completed = false;

    if (phase_1_completed_ && phase_2_completed_ && !phase_3_completed_)
    {
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS; // Phase 3, localization detects QR code, can calculate pose
            UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS);
        }

        if (x_first > 0.1) // See the beacon of the next lawn. End cross-region early
        {
            LOG_INFO("[CrossRegion] [Phase 3] In the case of the next lawn. Adjust to pass_point based on the beacon");

            phase_3_completed_ = true;

            if (phase_1_completed_ && phase_2_completed_ && phase_3_completed_)
            {
                phase_41_completed_ = true;
                phase_42_completed_ = true;
                phase_43_completed_ = false;
                if (phase_1_completed_ && phase_2_completed_ && phase_3_completed_)
                {
                    DealCrossRegionPhase_43(mark_loc_result, x_first, y_first, yaw_first);
                    DealCrossRegionPhase_44(mark_loc_result, cross_region_completed, x_first);

                    return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::Successed);
                }
            }
        }
        else // See the beacon of the current lawn
        {
            LOG_INFO("[CrossRegion] [Phase 3] In the case of the current lawn. Adjust to pass_point based on the beacon");

            if (edge_mode_direction_ == 1) // Edge following clockwise
            {
                LOG_INFO("[CrossRegion] [Phase 3] Not considering clockwise edge following cross-region scenario");
                // LOG_INFO("[CrossRegion] [Phase 3] In the case of clockwise edge following. Adjust to pass_point based on the beacon");
                // ClockwiseAlignPassPoints(x_first, y_first, yaw_first);
            }
            else // Edge following counterclockwise
            {
                LOG_INFO("[CrossRegion] [Phase 3] In the case of counterclockwise edge following. Adjust to pass_point based on the beacon");
                // CounterClockwiseAlignPassPoints(x_first, y_first, yaw_first);
                // ControlProcessToPassPoints(x_first, y_first, yaw_first);
                ControlProcessToPassPointsWithIMU(x_first, y_first, yaw_first);
            }

            LOG_INFO("[CrossRegion] [Phase 3] Phase 3 completed. Start walking along the channel");
            phase_3_completed_ = true; // Allow passage through the channel
        }
    }

    return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::InProgress);
}

CrossRegionAlgResult NavigationCrossRegionAlg::DealCrossRegionPhase_4(MarkLocationResult &mark_loc_result,
                                                                      PerceptionFusionResult &fusion_result,
                                                                      float &act_linear)
{
    bool cross_region_completed = false;

    if (phase_1_completed_ && phase_2_completed_ && phase_3_completed_ && is_walking_before_crossing_passage_)
    {
        LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] Has moved straight for a certain distance, start detecting beacons");

        //  1. Get precise beacon QR code pose
        float x_first = 0.0;
        float y_first = 0.0;
        float yaw_first = 0.0;
        DealCrossRegionPhase_41(mark_loc_result, fusion_result, cross_region_completed, act_linear);
        DealCrossRegionPhase_42(mark_loc_result, x_first, y_first, yaw_first);
        DealCrossRegionPhase_43(mark_loc_result, x_first, y_first, yaw_first);
        DealCrossRegionPhase_44(mark_loc_result, cross_region_completed, x_first);

        return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::Successed);
    }

    return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::InProgress);
}

void NavigationCrossRegionAlg::DealStage4EnteringPassage(const float &x_first, const float &y_first, const float &yaw_first,
                                                         float &act_linear)
{
    (void)x_first;
    (void)y_first;
    (void)yaw_first;
    (void)act_linear;

    if (phase_1_completed_ && phase_2_completed_ && phase_3_completed_ && !is_walking_before_crossing_passage_)
    {
        LOG_INFO("[CrossRegion] [EnteringPassage] x_after_adjustment_ = {}", x_after_adjustment_);
        if (x_after_adjustment_ < 0.0)
        {
            // ControlLinearMotion(0.0, x_after_adjustment_, cross_region_linear_, 1);
            ControlLinearMotionWithIMUThread(0.0, x_after_adjustment_, cross_region_linear_, 1);
            is_walking_before_crossing_passage_ = true;
        }
        else
        {
            is_walking_before_crossing_passage_ = true;
        }

        // Phase 4, just entered the channel and has moved straight for a certain distance
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::STAGE4_ENTERING_CHANNEL; // Phase 4, just entered the channel and has moved straight for a certain distance
            UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_ENTERING_CHANNEL);
        }
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_41(MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result,
                                                       bool &cross_region_completed, float &act_linear)
{
    if (!phase_41_completed_ && !phase_42_completed_ && !phase_43_completed_)
    {
        // FindBeaconsPhase_41(mark_loc_result, fusion_result, cross_region_completed);
        FindBeaconsPhase_41_New(mark_loc_result, fusion_result, cross_region_completed, act_linear);
    }

    UpdateStraightMotionTimingNew(act_linear);
    // UpdateStraightMotionTiming(act_linear);
}

void NavigationCrossRegionAlg::UpdateStraightMotionTimingNew(float act_linear)
{
    auto current_time = std::chrono::steady_clock::now();

    if (!is_straight_timing_started_)
    {
        straight_start_time_ = std::chrono::steady_clock::now();
        is_straight_timing_started_ = true;
        LOG_WARN("[CrossRegion] [Phase 4] Straight movement timing started");
    }

    float dt;
    if (last_distance_update_time_.time_since_epoch().count() == 0)
    {
        dt = 0.02f;
        last_distance_update_time_ = std::chrono::steady_clock::now();
    }
    else
    {
        dt = std::chrono::duration<float>(current_time - last_distance_update_time_).count();
    }

    last_distance_update_time_ = current_time; // Update timestamp

    // Calculate displacement increment at current speed (not absolute value)
    float delta_distance = act_linear * dt;
    accumulated_distance_ += delta_distance;

    // Calculate elapsed time
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - straight_start_time_);
    LOG_WARN("[CrossRegion] [Phase 4] Straight movement duration: {} seconds", duration.count());

    // Double condition check
    bool timeout = duration.count() >= CROSS_AREA_WALKING_TIMEOUT;
    bool over_distance = accumulated_distance_ >= cross_region_distance_threshold_;

    if (timeout || over_distance)
    {
        LOG_ERROR("[CrossRegion] Straight movement exceeded limit | Time: {}s Distance: {:.1f}m",
                  duration.count(), accumulated_distance_);
        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_CROSS_REGION_GRASS_TIMEOUT_EXCEPTION);

        // // Add remedial measures: slow down or stop
        // PublishVelocity(cross_region_linear_ * 0.5f, 0.0f, 1000);

        // // Try rotating a certain angle to search for the beacon
        // if (duration.count() >= CROSS_AREA_WALKING_TIMEOUT + 5)
        // {
        //     PublishVelocity(0.0f, 0.3f, 2000); // Rotate a certain angle to search for the beacon
        // }
    }
}

void NavigationCrossRegionAlg::UpdateStraightMotionTiming(float act_linear)
{
    auto current_time = std::chrono::steady_clock::now();
    // Only start timing during straight movement in non-grass or grass detection phase
    if (non_grass_area_reached_)
    {
        if (!is_straight_timing_started_)
        {
            straight_start_time_ = std::chrono::steady_clock::now();
            is_straight_timing_started_ = true;
            LOG_WARN("[CrossRegion] [Phase 4] Straight movement timing started");
        }

        float dt;
        if (last_distance_update_time_.time_since_epoch().count() == 0)
        {
            dt = 0.02f;
            last_distance_update_time_ = std::chrono::steady_clock::now();
        }
        else
        {
            dt = std::chrono::duration<float>(current_time - last_distance_update_time_).count();
        }
        last_distance_update_time_ = current_time; // Update timestamp

        // Calculate displacement increment at current speed (not absolute value)
        float delta_distance = act_linear * dt;
        accumulated_distance_ += delta_distance;

        // Calculate elapsed time
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - straight_start_time_);
        LOG_WARN("[CrossRegion] [Phase 4] Straight movement duration: {} seconds", duration.count());

        // Double condition check
        bool timeout = duration.count() >= CROSS_AREA_WALKING_TIMEOUT;
        bool over_distance = accumulated_distance_ >= cross_region_distance_threshold_;

        if (timeout || over_distance)
        {
            LOG_ERROR("[CrossRegion] Straight movement exceeded limit | Time: {}s Distance: {:.1f}m",
                      duration.count(), accumulated_distance_);
            PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_CROSS_REGION_GRASS_TIMEOUT_EXCEPTION);

            // // Add remedial measures: slow down or stop
            // PublishVelocity(cross_region_linear_ * 0.5f, 0.0f, 1000);

            // // Try rotating a certain angle to search for the beacon
            // if (duration.count() >= CROSS_AREA_WALKING_TIMEOUT + 5)
            // {
            //     PublishVelocity(0.0f, 0.3f, 2000); // Rotate a certain angle to search for the beacon
            // }
        }
    }
    else
    {
        // Accumulate distance in grass area
        float dt;
        if (last_grass_distance_update_time_.time_since_epoch().count() == 0)
        {
            dt = 0.02f; // Assume initial time step is 20ms
            last_grass_distance_update_time_ = std::chrono::steady_clock::now();
        }
        else
        {
            dt = std::chrono::duration<float>(current_time - last_grass_distance_update_time_).count();
        }
        last_grass_distance_update_time_ = current_time;

        float delta_distance = act_linear * dt;
        grass_area_accumulated_distance_ += delta_distance;

        // // Check if distance exceeds 2 meters
        // if (grass_area_accumulated_distance_ > 2.0f)
        // {
        //     LOG_ERROR("[CrossRegion] Walking distance in grass area exceeds 2m, non-grass area not found, accumulated distance: {:.2f}m", grass_area_accumulated_distance_);
        //     PublishVelocity(0.0f, 0.0f, 1000);
        //     PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_CROSS_ZONE_NON_LAWN_NOT_FOUND_EXCEPTION);
        // }

        // Reset timing flag when not in straight movement
        LOG_WARN("[CrossRegion] [Phase 4] Timing restarted");
        is_straight_timing_started_ = false;
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_42(MarkLocationResult &mark_loc_result,
                                                       float &x_first, float &y_first, float &yaw_first)
{
    if (phase_41_completed_ && !phase_42_completed_ && !phase_43_completed_)
    {
        StopContinuousLinearMotion();

        if (mark_loc_result.detect_status != 2 || mark_loc_result.mark_perception_direction != 0) // The beacon cannot calculate the pose or the mower is not facing the beacon
        {
            LOG_INFO("[CrossRegion] [Phase 4] Beacon cannot calculate pose, start Phase 1, straight cross-region");
            phase_41_completed_ = false;
            PublishZeroVelocity();

            // No beacon detected upon entering the channel
            if (mark_loc_result.detect_status == 0)
            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_LOC_NO_DETECT_BEACON; // Phase 4, localization does not detect QR code
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_LOC_NO_DETECT_BEACON);
            }
            else if (mark_loc_result.detect_status == 1)
            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_NO_POS; // Phase 4, localization detects QR code, cannot calculate pose
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_NO_POS);
            }
            else if (mark_loc_result.mark_perception_status != 0)
            {
                LOG_INFO("[CrossRegion] The mower is not facing the beacon, mark_perception_status != 0");
            }
        }
        else
        {
            LOG_INFO("[CrossRegion] [Phase 4] Localization can calculate pose");
            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_WITH_POS;
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_WITH_POS);
            }

            LOG_INFO("[CrossRegion] [Phase 4] Get precise beacon QR code pose");

            //  1. Get precise beacon QR code pose
            x_first = 0.0;
            y_first = 0.0;
            yaw_first = 0.0;
            GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_42_completed_);

            // // Within the confidence region
            // if (mark_loc_result.roi_confidence == 1)
            // {
            //     LOG_INFO("[CrossRegion] [Phase 4] Beacon appears in the confidence region");

            //     //  1. Get precise beacon QR code pose
            //     x_first = 0.0;
            //     y_first = 0.0;
            //     yaw_first = 0.0;
            //     GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_42_completed_);
            // }
            // else // Outside the confidence region
            // {
            //     LOG_INFO("[CrossRegion] [Phase 4] Beacon appears outside the confidence region");
            //     CorrectionOutsideConfidence(mark_loc_result); // Correct the car's orientation if the beacon is outside the confidence range
            // }
        }
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_43(MarkLocationResult &mark_loc_result,
                                                       const float &x_first, const float &y_first, const float &yaw_first)
{
    (void)mark_loc_result;
    if (phase_41_completed_ && phase_42_completed_ && !phase_43_completed_)
    {
        LOG_INFO("[CrossRegion] [Phase 4] Completed getting precise pose x_first = {}, y_first = {}, yaw_first = {}",
                 x_first, y_first, Radians2Degrees(yaw_first));

        // Adjust the car's pose to the cutoff point based on the obtained pose, and set the stopping distance
        if (edge_mode_direction_ == 1) // Edge following clockwise
        {
            LOG_INFO("[CrossRegion] [Phase 4] Not considering clockwise edge following cross-region scenario");

            // LOG_INFO("[CrossRegion] [Phase 4] In the case of clockwise edge following. Adjust to end_point based on the beacon");
            // ClockwiseAlignEndPoints(x_first, y_first, yaw_first);
        }
        else // Edge following counterclockwise
        {
            LOG_INFO("[CrossRegion] [Phase 4] In the case of counterclockwise edge following. Adjust to end_point based on the beacon");
            // CounterClockwiseAlignEndPoints(x_first, y_first, yaw_first);
            // ControlProcessToEndPoints(x_first, y_first, yaw_first);
            ControlProcessToEndPointsWithIMU(x_first, y_first, yaw_first);
        }

        LOG_INFO("[CrossRegion] [Phase 4] Phase 3 completed. Start adjusting to near the end point, then drive the car towards the target stop point");
        phase_43_completed_ = true;
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_44(MarkLocationResult &mark_loc_result, bool &cross_region_completed, const float &x_first)
{
    (void)mark_loc_result;
    if (phase_41_completed_ && phase_42_completed_ && phase_43_completed_)
    {
        // After adjusting to near the end point, drive the car towards the target stop point channel_stop_pose_x_= -0.5
        LOG_INFO("[CrossRegion] [Phase 4] After adjusting to near the end point, drive the car towards the target stop point");
        // ControlLinearMotion(channel_stop_pose_x_, x_first, cross_region_linear_, 1);
        ControlLinearMotionWithIMUThread(channel_stop_pose_x_, x_first, cross_region_linear_, 1);

        // Send a stop signal after reaching the specified position
        PublishVelocity(0.0, 0.0, 1000); // Keep publishing for 1s

        // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
        // ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // Turn right

        ControlLinearMotionWithIMUThread(0.3, 0.0, cross_region_linear_, 1);
        ControlRotaryMotionWithIMU(0.0, 2.35, cross_region_angular_); // Turn right
        cross_region_completed = true;
        LOG_INFO("[CrossRegion] [Phase 4] Cross-region algorithm execution finished");

        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION; // Phase 4, beacon detection exits cross-region
            UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION);
        }
    }
}

void NavigationCrossRegionAlg::ResetCrossRegionFlags()
{
    is_walking_before_crossing_passage_ = false;

    phase_1_completed_ = false;
    phase_2_completed_ = false;
    phase_3_completed_ = false;

    phase_41_completed_ = false;
    phase_42_completed_ = false;
    phase_43_completed_ = false;

    is_cooldown_active_ = false;
    last_cooldown_time_ = std::chrono::steady_clock::now();
    perception_drive_duration_ = std::chrono::seconds(0);

    frames_.clear();
    current_state_ = RegionState::IN_GRASS;
    next_paired_beacon_id_ = -1;     // Next pair of beacon IDs
    non_grass_area_reached_ = false; // Phase 4, new state flag
    emergency_stop_ = false;         // Emergency stop flag
    front_beacon_detected_ = false;
    is_first_non_grass_area_reached_ = true;
    // UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);

    // Reset straight timer
    is_straight_timing_started_ = false;
    straight_start_time_ = std::chrono::steady_clock::now();

    // IMU related member variables
    last_imu_timestamp_ = 0;

    is_first_linear_in_progress_ = false;
    linear_motion_completed_ = false;
    linear_start_time_ = std::chrono::steady_clock::now();

    // Reset timed movement variables
    timed_movement_in_progress_ = false;
    timed_movement_accumulated_distance_ = 0.0f;
    timed_movement_last_update_time_ = std::chrono::steady_clock::time_point{};

    accumulated_angle_ = 0.0;                          // Accumulated heading change due to interference
    is_correcting_ = false;                            // Correction state flag
    yaw_target_ = 0.0;                                 // Target heading angle for straight movement
    yaw_current_ = 0.0;                                // Current heading angle (IMU integration)
    last_imu_time_ = std::chrono::steady_clock::now(); // Last IMU update time
    is_first_imu_ = true;

    is_bias_calibrated_ = false; // Reset calibration state
    bias_z_ = 0.0f;
    bias_samples_.clear();

    accumulated_distance_ = 0.0;
    last_distance_update_time_ = std::chrono::steady_clock::time_point{};
    last_grass_distance_update_time_ = std::chrono::steady_clock::time_point{};

    // Reset beacon pairing error state
    beacon_pairing_error_active_ = false;
    beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();

    // Reset grass area distance
    grass_area_accumulated_distance_ = 0.0f;

    // Reset channel distance tracking variables
    channel_distance_tracking_started_ = false;
    channel_accumulated_distance_ = 0.0f;
    channel_distance_start_time_ = std::chrono::steady_clock::time_point{};
    channel_last_distance_update_time_ = std::chrono::steady_clock::time_point{};

    // Reset IMU processor state
    if (imu_processor_)
    {
        imu_processor_->ResetState();
    }

    x_after_adjustment_ = 0.0;

    mcu_exception_retry_count_ = 0;
    last_exception_time_ = std::chrono::steady_clock::time_point{};

    mark_id_distance_temp_ = 0.0f;
    get_pose_stage_ = false;
}

void NavigationCrossRegionAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationCrossRegionAlg::SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback)
{
    cross_region_running_state_callback_ = callback;
}

void NavigationCrossRegionAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};

    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);

    feature_data.push_back(feature);

    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationCrossRegionAlg::EdgeFollowDisable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, false);
}

void NavigationCrossRegionAlg::EdgeFollowEnable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, true);
}

void NavigationCrossRegionAlg::UpdateCrossRegionRunningState(CrossRegionRunningState state)
{
    if (cross_region_running_state_callback_)
    {
        cross_region_running_state_callback_(state);
    }
}

/**
 * @brief Phase 1. Find beacons along the edge
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::FindBeaconsPhase_1(const MarkLocationResult &mark_loc_result)
{
    if (mark_loc_result.mark_perception_status == 0) // The perception does not detect the beacon
    {
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::EDGE_FINDING_BEACON; // Add the state of finding the beacon along the edge
            UpdateCrossRegionRunningState(CrossRegionRunningState::EDGE_FINDING_BEACON);
        }

        LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 1] 1. The perception does not detect the beacon, enable edge following");
        EdgeFollowEnable(); // Enable edge following
    }
    else // The perception detects the beacon
    {
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::PER_FOUND_BEACON; // The perception localization has found the beacon
            UpdateCrossRegionRunningState(CrossRegionRunningState::PER_FOUND_BEACON);
        }

        LOG_INFO("[CrossRegion] [Phase 1] 2. The perception detects the beacon");
        if (mark_loc_result.mark_id_distance.size() <= 0) // The mark_id_distance of the localization has no value
        {
            LOG_INFO("[CrossRegion] [Phase 1] 2.1 The mark_id_distance of the localization has no value");
            HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
        }
        else // The mark_id_distance of the localization has a value
        {
            LOG_INFO("[CrossRegion] [Phase 1] 2.2 The mark_id_distance of the localization has a value");

            // Determine whether the beacon is valid. (mark_id_distance less than 50cm is considered that the current beacon is valid)
            int shortest_dis_inx = -1;
            // int paired_beacon_idx = -1;
            std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
            FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);
            // FingVaidBeaconPairIdx(mark_id_distance_vec, shortest_dis_inx, paired_beacon_idx);

            if (shortest_dis_inx == -1) // If the beacon is invalid. Do not operate, continue the previous action
            {
                LOG_INFO("[CrossRegion] [Phase 1]  2.2.1 The cross-region beacon is invalid");
                HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
            }
            else // If the beacon is valid. Detect whether the stack container is empty
            {
                LOG_INFO("[CrossRegion] [Phase 1]  2.2.2 The cross-region beacon is valid");
                LOG_INFO("[CrossRegion] [Phase 1]  2.2.2 The valid beacon is mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                mark_id_distance_temp_ = mark_id_distance_vec[shortest_dis_inx].distance;

                // Send the beacon id to the localization for cross-region
                SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);
                next_paired_beacon_id_ = PairNumber(mark_id_distance_vec[shortest_dis_inx].mark_id);
                LOG_INFO("[CrossRegion] [Phase 1]  2.2.2 The next pair of beacon id is {}", next_paired_beacon_id_);

                if (mark_loc_result.detect_status == 2 && mark_loc_result.mark_perception_direction == 0) // 小车正对信标
                {
                    LOG_INFO("[CrossRegion] [Phase 1]  2.2.2.1 The beacon can calculate the pose");

                    // Execute the cross-channel thread and close the edge following thread
                    LOG_INFO("[CrossRegion] [Phase 1]  2.2.2.1 Phase 1 is completed, start cross-region, close the edge following thread");
                    EdgeFollowDisable();
                    phase_1_completed_ = true;

                    // Reset the cooldown timestamp and activate the cooldown mechanism
                    last_cooldown_time_ = std::chrono::steady_clock::now();
                    is_cooldown_active_ = true;
                    // ResetAndActivateCooldown();
                }
                else
                {
                    LOG_INFO("[CrossRegion] [Phase 1]  2.2.2.2 Phase 1 is completed, start cross-region, perception and posture adjustment");
                    HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
                }
            }
        }
    }
}

void NavigationCrossRegionAlg::FindBeaconsPhase_41_New(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                                                       bool &cross_region_completed,
                                                       float &act_linear)
{
    // Initialize channel passing distance tracking if not started
    if (!channel_distance_tracking_started_)
    {
        channel_distance_tracking_started_ = true;
        channel_accumulated_distance_ = 0.0f;
        channel_distance_start_time_ = std::chrono::steady_clock::now();
        LOG_INFO("[CrossRegion] [Phase 4] Started channel distance tracking, fixed distance: {:.2f}m", channel_fixed_distance_);
    }

    // Update accumulated distance during movement
    if (act_linear > 0.01f) // Only accumulate when actually moving forward
    {
        auto current_time = std::chrono::steady_clock::now();
        if (channel_last_distance_update_time_.time_since_epoch().count() > 0)
        {
            float dt = std::chrono::duration<float>(current_time - channel_last_distance_update_time_).count();
            float delta_distance = act_linear * dt;
            channel_accumulated_distance_ += delta_distance;

            LOG_INFO("[CrossRegion] [Phase 4] Channel distance: {:.2f}m / {:.2f}m",
                     channel_accumulated_distance_, channel_fixed_distance_);
        }
        channel_last_distance_update_time_ = current_time;
    }

    bool beacon_detect_completed = false;
    BeaconDetectedPhase_4(mark_loc_result, beacon_detect_completed);

    if (beacon_detect_completed)
    {
        return;
    }

    // 检查跨区信标碰撞并进行避障
    bool collision_detected = false;
    float avoidance_angular = 0.0f;
    CheckCrossRegionBeaconCollision(fusion_result, collision_detected, avoidance_angular);

    // Check if exceeded fixed distance without beacon detection
    if (channel_accumulated_distance_ >= channel_fixed_distance_)
    {
        LOG_INFO("[CrossRegion] [Phase 4] Exceeded fixed channel distance ({:.2f}m) without beacon detection", channel_fixed_distance_);

        // Check if the current area is grass
        if (Nongrass2Grass(fusion_result))
        {
            LOG_INFO("[CrossRegion] [Phase 4] Current area is grass, starting displacement-based movement with beacon detection");

            bool beacon_found = false;
            bool movement_completed = false;

            // Convert from time-based (3.0s) to distance-based using cross_region_linear_ velocity
            float target_distance = 3.0f * cross_region_linear_; // distance = time * velocity
            TimedMovementWithBeaconDetection(target_distance, mark_loc_result, beacon_found, movement_completed, act_linear);

            if (beacon_found)
            {
                LOG_INFO("[CrossRegion] [Phase 4] Beacon detected during displacement-based movement, ending via beacon");
                return; // Exit via beacon detection
            }
            else if (movement_completed)
            {
                StopContinuousLinearMotion();

                LOG_INFO("[CrossRegion] [Phase 4] Displacement-based movement completed, ending cross-region");
                PublishVelocity(0.0, 0.0, 1000); // Stop

                // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
                // ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // Turn right

                ControlLinearMotionWithIMUThread(0.3, 0.0, cross_region_linear_, 1);
                ControlRotaryMotionWithIMU(0.0, 2.35, cross_region_angular_); // Turn right

                cross_region_completed = true;

                {
                    std::lock_guard<std::mutex> lck(cross_region_mutex_);
                    cross_region_state_ = CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION; // Phase 4, non-grass to grass exit cross-region
                    UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION);
                }
                return; // Exit function
            }
        }
        else
        {
            LOG_INFO("[CrossRegion] [Phase 4] Current area is non-grass, continue straight movement");
            if (collision_detected)
            {
                LOG_WARN("[CrossRegion] [Phase 4] Cross-region beacon collision detected, performing avoidance maneuver");
                PublishVelocity(cross_region_linear_, avoidance_angular); // 避障
            }
            else
            {
                PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Go straight with IMU correction
            }
        }
    }
    else
    {
        // Within fixed distance and no beacon detected, continue straight
        if (collision_detected)
        {
            LOG_WARN("[CrossRegion] [Phase 4] Cross-region beacon collision detected during straight movement, performing avoidance");
            PublishVelocity(cross_region_linear_, avoidance_angular); // 避障
        }
        else
        {
            LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] No beacon detected, continue straight through channel");
            PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Go straight with IMU correction
        }
    }
}

void NavigationCrossRegionAlg::TimedMovementWithBeaconDetection(float target_distance, const MarkLocationResult &mark_loc_result,
                                                                bool &beacon_found, bool &movement_completed,
                                                                float &act_linear)
{
    beacon_found = false;
    movement_completed = false;

    // Initialize displacement tracking if not already started
    if (!timed_movement_in_progress_)
    {
        timed_movement_accumulated_distance_ = 0.0f;
        timed_movement_last_update_time_ = std::chrono::steady_clock::now();
        timed_movement_in_progress_ = true;
        LOG_INFO("[CrossRegion] [TimedMovement] Starting displacement-based movement with beacon detection, target distance: {:.2f}m", target_distance);
    }

    // Update accumulated distance using actual velocity
    auto current_time = std::chrono::steady_clock::now();
    if (timed_movement_last_update_time_.time_since_epoch().count() > 0)
    {
        float dt = std::chrono::duration<float>(current_time - timed_movement_last_update_time_).count();
        float delta_distance = std::abs(act_linear) * dt; // Use absolute value to handle both forward and backward movement
        timed_movement_accumulated_distance_ += delta_distance;

        LOG_INFO_THROTTLE(200, "[CrossRegion] [TimedMovement] Distance progress: {:.3f}m / {:.2f}m",
                          timed_movement_accumulated_distance_, target_distance);
    }
    timed_movement_last_update_time_ = current_time;

    // Check for beacon detection during movement
    bool beacon_detect_completed = false;
    BeaconDetectedPhase_4(mark_loc_result, beacon_detect_completed);

    if (beacon_detect_completed)
    {
        LOG_INFO("[CrossRegion] [TimedMovement] Beacon detected at {:.3f}m, ending via beacon", timed_movement_accumulated_distance_);
        beacon_found = true;
        timed_movement_in_progress_ = false; // Reset displacement tracking state
        PublishVelocity(0.0, 0.0, 100);      // Stop immediately
        return;
    }

    // Check if target displacement is completed
    if (timed_movement_accumulated_distance_ >= target_distance)
    {
        LOG_INFO("[CrossRegion] [TimedMovement] Target distance {:.2f}m completed, continuing straight", target_distance);
        movement_completed = true;
        timed_movement_in_progress_ = false;                          // Reset displacement tracking state
        PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Continue straight movement with IMU correction
        return;
    }

    // Continue moving straight during the displacement period
    PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Move straight at cross_region_linear_ speed with IMU correction
}

void NavigationCrossRegionAlg::BeaconDetectedPhase_4(const MarkLocationResult &mark_loc_result, bool &beacon_detect_completed)
{
    beacon_detect_completed = false;

    // Check if beacon is detected within the fixed distance
    bool beacon_detected = (mark_loc_result.mark_perception_status == 1);

    if (beacon_detected)
    {
        LOG_INFO("[CrossRegion] [Phase 4] Beacon detected within channel distance");

        if (mark_loc_result.mark_id_distance.size() <= 0) // The mark_id_distance of the localization has no value
        {
            LOG_INFO("[CrossRegion] [Phase 4] 2.1 The mark_id_distance of the localization has no value");
            HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);

            beacon_detect_completed = true;
            return;
        }
        else // The mark_id_distance of the localization has a value
        {
            LOG_INFO("[CrossRegion] [Phase 4] 2.2 The mark_id_distance of the localization has a value");

            // Determine whether the beacon is valid. (mark_id_distance less than 50cm is considered that the current beacon is valid)
            int shortest_dis_inx = -1;
            int paired_beacon_idx = -1;
            std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
            // FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);
            FingVaidBeaconPairIdx(mark_id_distance_vec, shortest_dis_inx, paired_beacon_idx);

            if (shortest_dis_inx == -1) // If the beacon is invalid. Do not operate, continue the previous action
            {
                LOG_INFO("[CrossRegion] [Phase 4] 2.2.1 The cross-region beacon is invalid");
                HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);

                beacon_detect_completed = true;
                return;
            }
            else // If the beacon is valid
            {
                LOG_INFO("[CrossRegion] 2.2.2 The cross-region beacon is valid");
                LOG_INFO("[CrossRegion] 2.2.2 The valid beacon is mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                if (next_paired_beacon_id_ == mark_id_distance_vec[shortest_dis_inx].mark_id)
                {
                    LOG_INFO("[CrossRegion] [Phase 4] 2.2.2.1 Found the next beacon mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                    // Reset the beacon pairing error state
                    beacon_pairing_error_active_ = false;
                    LOG_INFO("[CrossRegion] [Phase 4] The beacon pairing is correct, reset the error state");

                    mark_id_distance_temp_ = mark_id_distance_vec[shortest_dis_inx].distance;

                    // Send the beacon id to the localization for cross-region
                    SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);

                    if (mark_loc_result.detect_status == 2 && mark_loc_result.mark_perception_direction == 0) // The mower is facing the beacon
                    {
                        LOG_INFO("[CrossRegion] [Phase 4]  2.2.2.1.1 The beacon can calculate the pose");
                        LOG_INFO("[CrossRegion] [Phase 4] Phase 1 is completed");
                        phase_41_completed_ = true;

                        // Call the function to reset the cooldown mechanism
                        // Reset the cooldown timestamp and activate the cooldown mechanism
                        last_cooldown_time_ = std::chrono::steady_clock::now();
                        is_cooldown_active_ = true;
                        // ResetAndActivateCooldown();

                        beacon_detect_completed = true;
                        return; // Exit function after successful beacon detection
                    }
                    else
                    {
                        LOG_INFO("[CrossRegion] [Phase 4]  2.2.2.1.2 The beacon cannot calculate the pose");
                        HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);

                        beacon_detect_completed = true;
                        return; // Exit function
                    }
                }
                else if (PairNumber(next_paired_beacon_id_) == mark_id_distance_vec[shortest_dis_inx].mark_id)
                {
                    LOG_ERROR("[CrossRegion] [Phase 4] 2.2.2.2 The next pair of beacons is incorrect");
                    PublishVelocity(0, 0, 100);

                    // If it is the first time a pairing error occurs, record the start time
                    if (!beacon_pairing_error_active_)
                    {
                        beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();
                        beacon_pairing_error_active_ = true;
                        LOG_INFO("[CrossRegion] [Phase 4] Detected a beacon pairing error, start timing");
                    }
                    else
                    {
                        // Calculate the error duration
                        auto current_time = std::chrono::steady_clock::now();
                        auto duration = std::chrono::duration_cast<std::chrono::seconds>(
                            current_time - beacon_pairing_error_start_time_);

                        // If the error lasts more than 120s, publish an exception
                        if (duration.count() >= 120) // 120+0.1*120=132s
                        {
                            LOG_ERROR("[CrossRegion] [Phase 4] The beacon pairing error has lasted for more than 20s, publish an exception");
                            PublishException(SocExceptionLevel::ERROR,
                                             SocExceptionValue::ALG_PNC_CROSS_ZONE_BEACON_PAIRING_EXCEPTION);
                            // Reset the error state after publishing the exception
                            beacon_pairing_error_active_ = false;
                        }
                    }

                    beacon_detect_completed = true;
                    return; // Exit function
                }
            }
        }
    }
}

/**
 * @brief Phase 4. Find beacons in the cross-region
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::FindBeaconsPhase_41(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                                                   bool &cross_region_completed)
{
    // New: Continuously detect state changes regardless of whether the non-grass area is reached
    if (CrossRegionFinished(fusion_result))
    {
        LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] Detected grass->non-grass->grass state transition");
        // ControlLinearMotion(cross_region_adjust_displace_, 0.0, cross_region_linear_, 1);

        // Continue to move straight for a certain distance, use the safe movement mode
        SafeLinearMotion(cross_region_adjust_displace_, 0.0,
                         cross_region_linear_, 1, mark_loc_result);

        if (!emergency_stop_)
        {
            LOG_INFO("[CrossRegion] Grass detected, cross-region algorithm execution finished");
            PublishVelocity(0.0, 0.0, 1000);
            cross_region_completed = true;

            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION; // Phase 4, non-grass to grass exit cross-region
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION);
            }

            return; // Return directly without executing subsequent logic
        }
    }

    // ====== New prerequisite: Must reach the non-grass area first ======
    if (!non_grass_area_reached_) /** Non-grass area has not been reached */
    {
        if (fusion_result.grass_detecte_status == GrassDetectStatus::NO_GRASS) /** Currently in the non-grass area */
        {
            non_grass_area_reached_ = true;          /** Non-grass area reached */
            grass_area_accumulated_distance_ = 0.0f; // Reset grass area distance when entering non-grass area
            LOG_INFO("[CrossRegion] [Phase 4] Non-grass area reached, start channel passing logic");

            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_NON_GRASS_REACHED; // Phase 4, non-grass area reached
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_NON_GRASS_REACHED);
            }
        }
        else /** Currently in the grass area */
        {
            // Use IMU-corrected straight movement instead of basic velocity
            PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f);
            LOG_INFO("[CrossRegion] [Phase 4] Non-grass state not reached yet, grass_detecte_status: {}", static_cast<int>(fusion_result.grass_detecte_status));
        }
    }
    else /** Non-grass area reached */
    {
        if (is_first_non_grass_area_reached_ && !linear_motion_completed_)
        {
            // Start initial non-blocking straight motion for 0.5m
            if (!is_first_linear_in_progress_)
            {
                LOG_INFO("[Phase 4] Start initial non-blocking straight motion for 0.5m");
                linear_start_time_ = std::chrono::steady_clock::now();
                is_first_linear_in_progress_ = true;
            }

            // Calculate elapsed time
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - linear_start_time_);

            // Calculate required time to move 0.5m: 0.5m / speed
            float required_time = 0.5 / cross_region_linear_ * 1000; // in milliseconds

            if (duration.count() >= required_time)
            {
                LOG_INFO("[Phase 4] Completed initial non-blocking straight motion for 0.5m");
                // PublishZeroVelocity();
                linear_motion_completed_ = true;
                is_first_non_grass_area_reached_ = false; // Reset flag
            }
            else
            {
                // Publish continuous straight velocity (non-timed)
                LOG_INFO("[Phase 4] Publish velocity: linear: %f, angular: %f", cross_region_linear_, 0.0);
                // PublishVelocityAndInterferenceCorrection(imu_data);
                PublishVelocity(cross_region_linear_, 0.0);
            }

            return; // During straight motion, return directly and do not execute subsequent beacon detection
        }

        // ====== Non-grass state has been reached, enter subsequent judgment ======
        if (mark_loc_result.mark_perception_status == 0) // The perception does not detect the beacon
        {
            LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] 1. The perception does not detect the beacon");
            LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] 1. Continue to move straight through the channel");

            // Use IMU-corrected straight movement for better heading control
            PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Go straight with IMU correction
        }
        else // The perception detects the beacon
        {
            LOG_INFO("[CrossRegion] [Phase 4] 2. The perception detects the beacon");
            if (mark_loc_result.mark_id_distance.size() <= 0) // The mark_id_distance of the localization has no value
            {
                LOG_INFO("[CrossRegion] [Phase 4] 2.1 The mark_id_distance of the localization has no value");
                HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
            }
            else // The mark_id_distance of the localization has a value
            {
                LOG_INFO("[CrossRegion] [Phase 4] 2.2 The mark_id_distance of the localization has a value");

                // Determine whether the beacon is valid. (mark_id_distance less than 50cm is considered that the current beacon is valid)
                int shortest_dis_inx = -1;
                int paired_beacon_idx = -1;
                std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
                // FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);
                FingVaidBeaconPairIdx(mark_id_distance_vec, shortest_dis_inx, paired_beacon_idx);

                if (shortest_dis_inx == -1) // If the beacon is invalid. Do not operate, continue the previous action
                {
                    LOG_INFO("[CrossRegion] [Phase 4] 2.2.1 The cross-region beacon is invalid");
                    HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
                }
                else // If the beacon is valid
                {
                    LOG_INFO("[CrossRegion] 2.2.2 The cross-region beacon is valid");
                    LOG_INFO("[CrossRegion] 2.2.2 The valid beacon is mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                    if (next_paired_beacon_id_ == mark_id_distance_vec[shortest_dis_inx].mark_id)
                    {
                        LOG_INFO("[CrossRegion] [Phase 4] 2.2.2.1 Found the next beacon mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                        // Reset the beacon pairing error state
                        beacon_pairing_error_active_ = false;
                        LOG_INFO("[CrossRegion] [Phase 4] The beacon pairing is correct, reset the error state");

                        // Send the beacon id to the localization for cross-region
                        SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);

                        if (mark_loc_result.detect_status == 2 && mark_loc_result.mark_perception_direction == 0) // The mower is facing the beacon
                        {
                            LOG_INFO("[CrossRegion] [Phase 4]  2.2.2.1.1 The beacon can calculate the pose");
                            LOG_INFO("[CrossRegion] [Phase 4] Phase 1 is completed");
                            phase_41_completed_ = true;

                            // Call the function to reset the cooldown mechanism
                            // Reset the cooldown timestamp and activate the cooldown mechanism
                            last_cooldown_time_ = std::chrono::steady_clock::now();
                            is_cooldown_active_ = true;
                            // ResetAndActivateCooldown();
                        }
                        else
                        {
                            LOG_INFO("[CrossRegion] [Phase 4]  2.2.2.1.2 The beacon cannot calculate the pose");
                            HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
                        }
                    }
                    else if (PairNumber(next_paired_beacon_id_) == mark_id_distance_vec[shortest_dis_inx].mark_id)
                    {
                        LOG_ERROR("[CrossRegion] [Phase 4] 2.2.2.2 The next pair of beacons is incorrect");
                        PublishVelocity(0, 0, 100);

                        // If it is the first time a pairing error occurs, record the start time
                        if (!beacon_pairing_error_active_)
                        {
                            beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();
                            beacon_pairing_error_active_ = true;
                            LOG_INFO("[CrossRegion] [Phase 4] Detected a beacon pairing error, start timing");
                        }
                        else
                        {
                            // Calculate the error duration
                            auto current_time = std::chrono::steady_clock::now();
                            auto duration = std::chrono::duration_cast<std::chrono::seconds>(
                                current_time - beacon_pairing_error_start_time_);

                            // If the error lasts more than 20s, publish an exception
                            if (duration.count() >= 20) // 20+0.1*20=22s
                            {
                                LOG_ERROR("[CrossRegion] [Phase 4] The beacon pairing error has lasted for more than 20s, publish an exception");
                                PublishException(SocExceptionLevel::ERROR,
                                                 SocExceptionValue::ALG_PNC_CROSS_ZONE_BEACON_PAIRING_EXCEPTION);
                                // Reset the error state after publishing the exception
                                beacon_pairing_error_active_ = false;
                            }
                        }
                    }
                }
            }
        }
    }
}

void NavigationCrossRegionAlg::ResetAndActivateCooldown()
{
    // Reset the cooldown timestamp and activate the cooldown mechanism
    // last_cooldown_time_ = std::chrono::steady_clock::now();
    // is_cooldown_active_ = true;
}

bool NavigationCrossRegionAlg::SetMarkLocationMarkId(int mark_id)
{
    if (set_mark_id_callback_)
    {
        return set_mark_id_callback_(mark_id);
    }
    return false;
}

void NavigationCrossRegionAlg::SetMarkLocationMarkIdCallback(std::function<bool(int)> callback)
{
    set_mark_id_callback_ = callback;
}

void NavigationCrossRegionAlg::FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx)
{
    float min_distance = std::numeric_limits<float>::max();
    shortest_dis_inx = -1;

    for (size_t i = 0; i < mark_id_distance_vec.size(); ++i)
    {
        if (mark_id_distance_vec[i].distance < mark_distance_threshold_) // 50cm
        {
            if (mark_id_distance_vec[i].distance < min_distance)
            {
                min_distance = mark_id_distance_vec[i].distance;
                shortest_dis_inx = i;
            }
        }
    }
}

void NavigationCrossRegionAlg::FingVaidBeaconPairIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx, int &paired_beacon_idx)
{
    float min_distance = std::numeric_limits<float>::max();
    shortest_dis_inx = -1;
    paired_beacon_idx = -1;

    // 收集所有符合距离阈值要求的信标索引
    std::vector<int> valid_indices;

    for (size_t i = 0; i < mark_id_distance_vec.size(); ++i)
    {
        LOG_INFO("[CrossRegion] Checking beacon: mark_id = {}, distance = {:.3f}m",
                 mark_id_distance_vec[i].mark_id, mark_id_distance_vec[i].distance);
        if (mark_id_distance_vec[i].distance < mark_distance_threshold_) // 距离小于阈值
        {
            valid_indices.push_back(i);
            LOG_INFO("[CrossRegion] Valid beacon found: mark_id = {}, distance = {:.3f}m",
                     mark_id_distance_vec[i].mark_id, mark_id_distance_vec[i].distance);
        }
    }

    // 如果没有符合距离要求的信标，直接返回
    if (valid_indices.empty())
    {
        LOG_INFO("[CrossRegion] No valid beacons found within distance threshold {:.3f}m", mark_distance_threshold_);
        return;
    }

    // 在符合距离要求的信标中查找最小距离的索引
    for (int idx : valid_indices)
    {
        if (mark_id_distance_vec[idx].distance < min_distance)
        {
            min_distance = mark_id_distance_vec[idx].distance;
            shortest_dis_inx = idx;
        }
    }

    // 在符合距离要求的信标中查找匹配 next_paired_beacon_id_ 的信标
    for (int idx : valid_indices)
    {
        if (next_paired_beacon_id_ == mark_id_distance_vec[idx].mark_id)
        {
            paired_beacon_idx = idx;
            LOG_INFO("[CrossRegion] Found paired beacon: mark_id = {}, distance = {:.3f}m",
                     mark_id_distance_vec[idx].mark_id, mark_id_distance_vec[idx].distance);
            break;
        }
    }

    // 优先选择匹配 next_paired_beacon_id_ 的信标，如果没有则选择最小距离的信标
    if (paired_beacon_idx != -1)
    {
        shortest_dis_inx = paired_beacon_idx;
        LOG_INFO("[CrossRegion] Selected paired beacon: mark_id = {}, distance = {:.3f}m",
                 mark_id_distance_vec[shortest_dis_inx].mark_id, mark_id_distance_vec[shortest_dis_inx].distance);
    }
    else if (shortest_dis_inx != -1)
    {
        LOG_INFO("[CrossRegion] Selected closest beacon: mark_id = {}, distance = {:.3f}m",
                 mark_id_distance_vec[shortest_dis_inx].mark_id, mark_id_distance_vec[shortest_dis_inx].distance);
    }
}

void NavigationCrossRegionAlg::HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active)
{
    if (is_cooldown_active) // Cooldown mechanism is activated
    {
        LOG_INFO("[CrossRegion] Cooldown mechanism is activated");

        // Perception-driven cooldown time
        auto current_time = std::chrono::steady_clock::now();
        perception_drive_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_cooldown_time_);

        // Print the time difference
        LOG_INFO("[CrossRegion] The perception-driven cooldown timer along the edge (seconds): ({})", perception_drive_duration_.count());

        HandleEdgeCooldownMechanism(mark_loc_result, is_cooldown_active, perception_drive_duration_, perception_drive_cooldown_time_threshold_);
    }
    else // Cooldown mechanism is not activated
    {
        LOG_INFO("[CrossRegion] Cooldown mechanism is not activated");

        LOG_INFO("[CrossRegion] Enable perception-driven, and close edge following at the same time!");

        EdgeFollowDisable();
        PerceptionBasedAdjustment(mark_loc_result);
    }
}

void NavigationCrossRegionAlg::HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                                           std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold) // Perception detection result
{
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        LOG_INFO("[CrossRegion] The timing time exceeds ({}) seconds, the cooldown ends", perception_drive_cooldown_time_threshold);

        LOG_INFO("[CrossRegion] Enable perception-driven, and close edge following");
        EdgeFollowDisable();
        PerceptionBasedAdjustment(mark_loc_result);
    }
    else
    {
        // Cooldown is not over, skip execution
        LOG_INFO("[CrossRegion] The cooldown time is not over, it does not exceed ({}) seconds", perception_drive_cooldown_time_threshold);

        EdgeFollowEnable(); // Enable edge following
        LOG_INFO("[CrossRegion] Enable edge following");
    }
}

void NavigationCrossRegionAlg::HandleCrossRegionPerceptionBeaconDetection(const MarkLocationResult &mark_loc_result,
                                                                          bool &is_cooldown_active)
{
    if (is_cooldown_active) // Cooldown mechanism is activated
    {
        LOG_INFO("[CrossRegion] Cooldown mechanism is activated");

        // Perception-driven cooldown time
        auto current_time = std::chrono::steady_clock::now();
        perception_drive_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_cooldown_time_);

        // Print the time difference
        LOG_INFO("[CrossRegion] The perception-driven cooldown timer along the edge (seconds): ({})", perception_drive_duration_.count());
        HandleCrossRegionCooldownMechanism(mark_loc_result, is_cooldown_active,
                                           perception_drive_duration_, perception_drive_cooldown_time_threshold_);
    }
    else // Cooldown mechanism is not activated
    {
        LOG_INFO("[CrossRegion] Cooldown mechanism is not activated");

        LOG_INFO("[CrossRegion] Enable perception-driven");

        StopContinuousLinearMotion();
        PerceptionBasedAdjustment(mark_loc_result);
    }
}

void NavigationCrossRegionAlg::HandleCrossRegionCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                                                  std::chrono::seconds &perception_drive_duration,
                                                                  int &perception_drive_cooldown_time_threshold)
{
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        LOG_INFO("[CrossRegion] The timing time exceeds ({}) seconds, the cooldown ends!", perception_drive_cooldown_time_threshold);

        LOG_INFO("[CrossRegion] Enable perception-driven");
        StopContinuousLinearMotion();
        PerceptionBasedAdjustment(mark_loc_result);
    }
    else
    {
        // Cooldown is not over, skip execution
        LOG_INFO("[CrossRegion] The cooldown time is not over, it does not exceed ({}) seconds", perception_drive_cooldown_time_threshold);

        // Drive the car to go straight through the channel
        LOG_INFO("[CrossRegion] Drive the car to go straight through the channel");
        PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Go straight with IMU correction
    }
}

/**
 * @brief
 *
 * @param detect_status
 * @return true Rotation in place is completed or QR code is detected
 * @return false Rotation in place is not completed
 */
bool NavigationCrossRegionAlg::ProcessPassageRotate(int mark_perception_status)
{
    bool ret = false;
    EdgeFollowDisable();

    // Calculate the rotation time in place
    if (passage_rotate_time_ == 0 && passage_rotate_begin_time_ == 0)
    {
        passage_rotate_time_ = (2 * M_PI / cross_region_angular_) * 1000; // Turning duration ms
        passage_rotate_begin_time_ = GetSteadyClockTimestampMs();
    }

    if (mark_perception_status == 1) // 0 means the beacon is not perceived (detected); 1 means the beacon is perceived (detected)
    {
        passage_rotate_time_ = 0;
        passage_rotate_begin_time_ = 0;
        LOG_INFO_THROTTLE(1000, "Passage rotate stage, find passage or QR code, stop rotate stage!");
        PublishVelocity(0, 0);
        return true;
    }

    // Execute the rotation process
    uint64_t run_time = GetSteadyClockTimestampMs() - passage_rotate_begin_time_;
    LOG_INFO_THROTTLE(1000, "Passage rotate stage, run time {} rotate_time {}!", run_time, passage_rotate_time_);
    if (run_time < passage_rotate_time_)
    {
        PublishVelocity(0, cross_region_angular_); // Rotate left
        ret = false;
    }
    else
    {
        LOG_INFO_THROTTLE(1000, "Recharge rotate stage finished!");
        passage_rotate_time_ = 0;
        passage_rotate_begin_time_ = 0;
        ret = true;
    }

    return ret;
}

/**
 * @brief Get the Precise Position Of Beacon object
 *
 * @param mark_loc_result
 * @param x_first
 * @param y_first
 * @param yaw_first
 * @param is_get_precise_position Whether the precise pose has been obtained
 * @note Drive close to the beacon until the precise QR code solution pose is obtained within the threshold range (close distance improves the accuracy of the solution)
 */
void NavigationCrossRegionAlg::GetPrecisePositionOfBeacon(MarkLocationResult &mark_loc_result,
                                                          float &x_first, float &y_first, float &yaw_first,
                                                          bool &is_get_precise_position)
{
    // If the solution is obtained, according to the solved pose, drive the car to reach the specified Euclidean distance range (close distance improves the accuracy of the solution)
    float x = mark_loc_result.xyzrpw.x;
    float y = mark_loc_result.xyzrpw.y;

    (void)x;
    (void)y;

    if (hypotf(x, y) > max_distance_threshold_)
    {
        // Drive the car to reach the maximum Euclidean distance range
        LOG_INFO("[CrossRegion] Drive the car to reach the maximum Euclidean distance range");
        // PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Move forward with IMU correction
        PublishVelocity(cross_region_linear_, 0.0f); // Move forward
    }
    else if (hypotf(x, y) < min_distance_threshold_)
    {
        // Drive the car to reach outside the minimum Euclidean distance range
        LOG_INFO("[CrossRegion] Drive the car to reach outside the minimum Euclidean distance range");
        // PublishVelocityWithIMUCorrection(-cross_region_linear_, 0.0f); // Move forward with IMU correction
        PublishVelocity(-cross_region_linear_, 0); // Reverse
    }
    else
    {
        PublishVelocity(0.0, 0.0, 100); // Stop the car for 100ms

        // Assign initial values to x_first, y_first, yaw_first
        x_first = mark_loc_result.xyzrpw.x;
        y_first = mark_loc_result.xyzrpw.y;
        yaw_first = mark_loc_result.xyzrpw.w;

        // Add a loop to continue to obtain the latest QR code pose during sleep
        for (int i = 0; i < 30; ++i) // Get a new pose every 50ms, a total of 2 seconds
        {
            bool is_pose_resolved = false; // Whether the re-solution pose is successful

            {
                std::lock_guard<std::mutex> lck(mark_loc_mutex_);
                const float epsilon = 1e-6; // Tolerance range
                if (std::fabs(mark_loc_result_law_.xyzrpw.x + 1.0) < epsilon &&
                    std::fabs(mark_loc_result_law_.xyzrpw.y + 1.0) < epsilon) // The original beacon values x, y are both -1, indicating that detect_status is not 2, and it is not detected
                {
                    LOG_INFO("[CrossRegion] The original beacon values x, y are both -1, indicating that detect_status is not 2, and it is not detected");
                    is_pose_resolved = false;
                }
                else
                {
                    LOG_INFO("[CrossRegion] It is judged that detect_status is 2, and it is detected");
                    mark_loc_result = mark_loc_result_;
                    is_pose_resolved = true; // Re-solution pose is successful, execute coordinate transformation
                }
            }

            if (is_pose_resolved) // Execute coordinate transformation
            {
                x_first = mark_loc_result.xyzrpw.x;
                y_first = mark_loc_result.xyzrpw.y;
                yaw_first = mark_loc_result.xyzrpw.w;
            }

            LOG_INFO("[CrossRegion] Get the new solution pose x_first = ({}) , y_first = ({}) , yaw_first = ({})",
                     x_first, y_first, Radians2Degrees(yaw_first));

            // Sleep
            // std::this_thread::sleep_for(std::chrono::seconds(1));
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }

        is_get_precise_position = true; // Phase 2 is completed
        LOG_INFO("[CrossRegion] Complete getting the new solution pose x_first = ({}) , y_first = ({}) , yaw_first = ({})",
                 x_first, y_first, Radians2Degrees(yaw_first));
    }
}
/**
 * @brief Correct the car's steering when the beacon is outside the confidence range
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::CorrectionOutsideConfidence(const MarkLocationResult &mark_loc_result)
{
    // Rotate according to the beacon direction given by the localization to find the high confidence area
    if (mark_loc_result.target_direction == static_cast<int>(Direction::LEFT_TURN)) // Turn left
    {
        PublishVelocity(0, cross_region_angular_);
        LOG_INFO("[CrossRegion] The beacon appears outside the confidence area! Turn left");
    }
    else if (mark_loc_result.target_direction == static_cast<int>(Direction::RIGHT_TURN)) // Turn right
    {
        PublishVelocity(0, -cross_region_angular_);
        LOG_INFO("[CrossRegion] The beacon appears outside the confidence area! Turn right");
    }
    else
    {
        LOG_INFO("[CrossRegion] Beacon confidence area detection failed");
    }
}

/**
 * @brief In the case of clockwise edge following. Adjust to pass_point based on the beacon
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ClockwiseAlignPassPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = channel_width_ / 2;
    // Determine whether the car is on the left or right side of the beacon. Clockwise edge following
    if (y_first >= 0) // On the left side of the beacon
    {
        // Determine whether the car is on the left or right side of the channel center
        if (y_first <= pass_point) // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon left, pass point right, turn left (or right) go straight turn right y_first = {}, yaw_first = {}",
                     y_first, Radians2Degrees(yaw_first));
            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // Turn left
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // Pass point right.
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // Move to the pass point (0, pass_point)
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, -1);
                // Turn right
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
        }
        else // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon left, pass point left, turn left (or right) go straight turn left y_first = {}, yaw_first = {}",
                     y_first, Radians2Degrees(yaw_first));
            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // Turn left
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // Pass point right.
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // Move to the pass point (0, channel_width_ / 2)
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);
                // Turn left
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // On the right side of the beacon
    {
        // 1. First, according to the beacon coordinates, drive the car to the left side of its beacon
        if (x_first > adjust_mode_x_direction_threshold_) // Default -0.5
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon right, pass point right, turn right (left turn) go straight turn right straight line x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            if (fabsf(adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // On the right side of the beacon and the pass point, turn right or left
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);
                // Move the car
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);
                // Turn right
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
            else
            {
                // On the right side of the beacon and the pass point, turn right or left
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);
                // Move the car to (x_first - adjust_mode_x_direction_threshold_, y_first)
                ControlLinearMotion(adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, 1);
                // Turn right
                ControlRotaryMotion(M_PI / 2, M_PI, cross_region_angular_);
                // Move the car
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);
                // Turn right
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
        }
        else // Outside the -adjust_mode_x_direction_threshold_ range
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon right, pass point right, turn right (left turn) go straight x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));
            // On the right side of the beacon and the pass point, turn right or left
            ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

            // Move the car to (x_first, pass_point - y_first)
            ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

            // Turn right
            ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
        }
    }
}

/**
 * @brief Rotary motion control
 *
 * @param yaw_des
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    float sign = UnifyAngle(yaw_des - yaw_first) >= 0.0 ? 1.0 : -1.0; // Default rotation direction 1.0 turn left -1.0 turn right
    if (sign > 0)
    {
        LOG_INFO("[CrossRegion] Rotation direction: turn left");
    }
    else
    {
        LOG_INFO("[CrossRegion] Rotation direction: turn right");
    }

    float ang_err = fabsf(UnifyAngle(yaw_des - yaw_first));
    uint64_t t = (ang_err / vel_angular) * 1000; // Turning duration ms
    LOG_INFO("[CrossRegion] Rotation angle = {}", Radians2Degrees(ang_err));
    LOG_INFO("[CrossRegion] Angular velocity = {}, time = {}", sign * vel_angular, ang_err / vel_angular);
    PublishVelocity(0, sign * vel_angular, t);
}

/**
 * @brief Linear motion control
 *
 * @param pass_point
 * @param location
 */
void NavigationCrossRegionAlg::ControlLinearMotion(const float &pass_point, const float &location,
                                                   const float &vel_linear, const int &reverse)
{
    float dis = fabsf(pass_point - location);
    uint64_t t = (dis / vel_linear) * 1000;
    LOG_INFO("[CrossRegion] Straight line distance dis = {}", dis);
    LOG_INFO("[CrossRegion] Straight line speed = {}, time = {}", reverse * vel_linear, dis / vel_linear);
    PublishVelocity(reverse * vel_linear, 0, t);
}

/**
 * @brief Linear motion control with IMU-based heading correction
 *
 * @param pass_point Target position
 * @param location Current position
 * @param vel_linear Linear velocity
 * @param reverse Direction (1 for forward, -1 for backward)
 * @param target_yaw Target heading angle for correction (default 0.0)
 */
void NavigationCrossRegionAlg::ControlLinearMotionWithIMU(const float &pass_point, const float &location,
                                                          const float &vel_linear, const int &reverse, const float &target_yaw)
{
    // Check if IMU processor is available and calibrated
    if (!imu_processor_ || !imu_processor_->IsBiasCalibrated())
    {
        LOG_WARN("[CrossRegion] IMU processor not available or not calibrated, falling back to basic linear motion");
        ControlLinearMotion(pass_point, location, vel_linear, reverse);
        return;
    }

    float total_distance = fabsf(pass_point - location);
    float remaining_distance = total_distance;
    const float step_distance = 0.05f;      // Move 5cm each step for heading correction
    const float heading_tolerance = 0.087f; // 5 degrees tolerance
    const float correction_gain = 0.5f;     // Angular velocity correction gain

    LOG_INFO("[CrossRegion] Starting IMU-corrected linear motion: distance={:.3f}m, target_yaw={:.1f}°",
             total_distance, target_yaw * 180.0f / M_PI);

    auto start_time = std::chrono::steady_clock::now();

    while (remaining_distance > 0.01f) // 1cm precision
    {
        // Get current heading from IMU
        float current_yaw = imu_processor_->getCurrentYaw();
        float heading_error = target_yaw - current_yaw;

        // Normalize heading error to [-π, π]
        while (heading_error > M_PI)
            heading_error -= 2 * M_PI;
        while (heading_error < -M_PI)
            heading_error += 2 * M_PI;

        // Calculate step distance (smaller of remaining distance or step size)
        float step_dist = std::min(remaining_distance, step_distance);
        uint64_t step_time = (step_dist / vel_linear) * 1000;

        // Calculate angular velocity for heading correction
        float angular_velocity = 0.0f;
        if (fabsf(heading_error) > heading_tolerance)
        {
            angular_velocity = heading_error * correction_gain;
            // Limit angular velocity to prevent excessive rotation
            const float max_angular_vel = 0.5f; // rad/s
            angular_velocity = std::max(-max_angular_vel, std::min(max_angular_vel, angular_velocity));

            LOG_INFO_THROTTLE(500, "[CrossRegion] Heading correction: error={:.1f}°, angular_vel={:.3f}rad/s",
                              heading_error * 180.0f / M_PI, angular_velocity);
        }

        // Execute movement with heading correction
        PublishVelocity(reverse * vel_linear, angular_velocity, step_time);

        remaining_distance -= step_dist;

        LOG_INFO_THROTTLE(200, "[CrossRegion] IMU linear motion progress: remaining={:.3f}m, heading={:.1f}°",
                          remaining_distance, current_yaw * 180.0f / M_PI);

        // Safety timeout check (prevent infinite loop)
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration<float>(current_time - start_time).count();
        if (elapsed > (total_distance / vel_linear) * 3.0f) // 3x expected time
        {
            LOG_WARN("[CrossRegion] IMU linear motion timeout, stopping");
            break;
        }
    }

    // Final stop
    PublishVelocity(0.0f, 0.0f, 100);

    LOG_INFO("[CrossRegion] IMU-corrected linear motion completed: target_distance={:.3f}m", total_distance);
}

/**
 * @brief Publish velocity with IMU-based heading correction for continuous straight movement
 *
 * @param expect_linear_velocity Linear velocity for straight movement
 * @param target_yaw Target heading angle for correction (default 0.0)
 */
void NavigationCrossRegionAlg::PublishVelocityWithIMUCorrection(float expect_linear_velocity, const float &target_yaw)
{
    // Check if IMU processor is available and calibrated
    if (!imu_processor_ || !imu_processor_->IsBiasCalibrated())
    {
        // Fall back to basic velocity publishing without correction
        PublishVelocity(expect_linear_velocity, 0.0f);
        return;
    }

    imu_processor_->StartContinuousLinearMotion(expect_linear_velocity, target_yaw);
}

void NavigationCrossRegionAlg::StopContinuousLinearMotion()
{
    if (imu_processor_)
    {
        imu_processor_->StopContinuousLinearMotion();
    }
}

/**
 * @brief Linear motion control using IMU processor thread-based control
 *
 * @param pass_point Target position
 * @param location Current position
 * @param vel_linear Linear velocity
 * @param reverse Direction (1 for forward, -1 for backward)
 * @param target_yaw Target heading angle for correction (default 0.0)
 */
void NavigationCrossRegionAlg::ControlLinearMotionWithIMUThread(const float &pass_point, const float &location,
                                                                const float &vel_linear, const int &reverse, const float &target_yaw)
{
    // Check if IMU processor is available and calibrated
    if (!imu_processor_ || !imu_processor_->IsBiasCalibrated())
    {
        LOG_WARN("[CrossRegion] IMU processor not available or not calibrated, falling back to basic linear motion");
        ControlLinearMotion(pass_point, location, vel_linear, reverse);
        return;
    }

    float total_distance = fabsf(pass_point - location);
    float expect_velocity = reverse * vel_linear;

    LOG_INFO("[CrossRegion] Starting IMU thread-based linear motion: distance={:.3f}m, velocity={:.3f}m/s, target_yaw={:.1f}°",
             total_distance, expect_velocity, target_yaw * 180.0f / M_PI);

    imu_processor_->StartLinearMotionControl(total_distance, expect_velocity, 0.0f);

    while (imu_processor_->IsLinearMotionControlActive())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));

        // // Check if we need to stop (for example, if a stop signal is received)
        // if (mower_running_state_ == MowerRunningState::PAUSE)
        // {
        //     imu_processor_->StopLinearMotionControl();
        //     break;
        // }
    }

    PublishVelocity(0.0f, 0.0f, 100);

    LOG_INFO("[CrossRegion] IMU thread-based linear motion completed");
}

void NavigationCrossRegionAlg::ControlProcessToPassPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = 0.55; // 0.375 Pass point: offset from the beacon in the y-axis direction
    // float yaw_des = M_PI / 4;     // 45 Expected adjustment angle
    // float yaw_compare = M_PI / 6; // 30 degrees (used for comparison, although not currently used)

    // Use a local variable to save the current angle for easy updating
    float current_yaw = yaw_first;
    bool need_adjust = false;
    float target_yaw = current_yaw;

    // Determine whether the direction needs to be adjusted
    // If the current angle is in the range of [0, 30°], the target angle is 45° ($$\pi/4$$)
    if (current_yaw >= 0 && current_yaw <= M_PI / 6)
    {
        target_yaw = M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [150°, 180°], the target angle is 135° ($$3\pi/4$$)
    else if (current_yaw >= 5 * M_PI / 6 && current_yaw <= M_PI)
    {
        target_yaw = 3 * M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [0, -30°], the target angle is -45° ($$-\pi/4$$)
    else if (current_yaw >= -M_PI / 6 && current_yaw < 0)
    {
        target_yaw = -M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [-180°, -150°], the target angle is -135° ($$-3\pi/4$$)
    else if (current_yaw <= -5 * M_PI / 6 && current_yaw >= -M_PI)
    {
        target_yaw = -3 * M_PI / 4;
        need_adjust = true;
    }

    // If the direction needs to be adjusted, perform the rotation first
    if (need_adjust)
    {
        LOG_INFO("[CrossRegion] Adjust direction: from {} to {}",
                 Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
        ControlRotaryMotion(target_yaw, current_yaw, cross_region_angular_);
        // Assume that after the rotation is completed, the direction of the car is updated
        current_yaw = target_yaw;
    }

    // Determine the position of the car relative to the beacon based on y_first, and determine which pass point to move to
    if (y_first <= 0) // The car is on the right side of the beacon
    {
        // Determine whether the car is on the left or right side of the pass point
        if (y_first <= -pass_point) // The car is on the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon right, and on the right side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            // Calculate the coordinates of the target point B, the y coordinate of the target pass point is -pass_point
            ComputeB(x_first, y_first, current_yaw, -pass_point, B_x, distance);
            // Move forward to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
        else // The car is on the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon right, and on the left side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -pass_point, B_x, distance);
            // Move backward to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1);
        }
    }
    else // The car is on the left side of the beacon
    {
        if (y_first <= pass_point) // The car is on the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon left, and on the right side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            // Calculate the coordinates of the target point B, the y coordinate of the target pass point is pass_point
            ComputeB(x_first, y_first, current_yaw, pass_point, B_x, distance);
            // Move backward to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1);
        }
        else // The car is on the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon left, and on the left side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, pass_point, B_x, distance);
            // Move forward to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
    }

    // After moving to the pass point, adjust the direction of the car to 0 degrees
    LOG_INFO("[CrossRegion] Adjust direction to 0 degrees");
    ControlRotaryMotion(0.0, current_yaw, cross_region_angular_);
}

/**
 * @brief Calculate the x coordinate of point B and the y coordinates of points A and B
 *
 * @param A_x
 * @param A_y
 * @param yaw
 * @param B_y
 * @param B_x
 * @param distance
 */
void NavigationCrossRegionAlg::ComputeB(float A_x, float A_y, float yaw, float B_y, float &B_x, float &distance)
{
    // Define a very small constant to determine whether floating point numbers are close to 0
    const float EPS = 1e-6;

    // Determine whether sin(yaw) is large enough (not close to 0)
    if (std::fabs(std::sin(yaw)) > EPS)
    {
        // Calculate parameter t
        float t = (B_y - A_y) / std::sin(yaw);
        // Calculate the x coordinate of B according to t
        B_x = A_x + t * std::cos(yaw);
        // The Euclidean distance is equal to |t| (unit direction vector)
        distance = std::fabs(t);
    }
    else
    {
        // When sin(yaw) is approximately 0, it means that the line is a horizontal line
        // At this time, the y coordinates of A and B must be equal, otherwise B is not on the line
        if (std::fabs(B_y - A_y) > EPS)
        {
            LOG_ERROR("[CrossRegion] Error: For a horizontal line, the y coordinate of B must be equal to the y coordinate of A!");

            // Handle error situation: here set B_x to A_x and set the distance to 0
            B_x = A_x;
            distance = 0;
        }
        else
        {
            // When the y coordinates of A and B are the same, the x coordinate of point B on the horizontal line cannot be uniquely determined
            // Here, the default is B_x = A_x, and the distance is 0
            B_x = A_x;
            distance = 0;
        }
    }
}

/**
 * @brief In the case of counterclockwise edge following. Adjust to pass_point based on the beacon
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 * @note Edge following is counterclockwise
 */
void NavigationCrossRegionAlg::CounterClockwiseAlignPassPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = -channel_width_ / 2;

    // Determine whether the car is on the left or right side of the beacon. Counterclockwise edge following
    if (y_first <= 0) // On the right side of the beacon
    {
        // Determine whether the car is on the left or right side of the channel center
        if (y_first <= pass_point) // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the right side of the beacon, on the right side of the pass point, turn left (or right) go straight turn right y_first = {}, yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // On the right side of the pass point. Turn right
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the right side of the pass point. Turn left
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // Move to the pass point (0, pass_point)
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

                // Turn right
                LOG_INFO("[CrossRegion] [Phase 3] Turn right Rotation angle = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
        }
        else // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the right side of the beacon, on the left side of the pass point, turn left (turn right) go straight turn left y_first = {}, yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // On the left side of the pass point. Turn right
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the left side of the pass point. Turn right
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // Move to the pass point (0, pass_point)
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, -1); // Reverse

                // Turn left
                LOG_INFO("[CrossRegion] [Phase 3] Turn left Rotation angle = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // On the left side of the beacon
    {
        // 1. First, according to the beacon coordinates, drive the car to the right side of its beacon
        if (x_first > adjust_mode_x_direction_threshold_) // Default -adjust_mode_x_direction_threshold_
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the left side of the beacon, on the left side of the pass point, turn right (turn left) go straight turn left straight line x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            if (fabsf(adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // On the left side of the pass point. Turn right
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

                // Move the car
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

                // Turn left
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
            else
            {
                // On the left side of the beacon and the pass point, turn right or left
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);

                // Move the car
                ControlLinearMotion(adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, 1);

                // Turn left
                ControlRotaryMotion(-M_PI / 2, -M_PI, cross_region_angular_);

                // Move the car
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

                // Turn left
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
        }
        else // Outside the -adjust_mode_x_direction_threshold_ range
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon left, pass point left, turn right (turn left) go straight x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            // On the left side of the beacon and the pass point, turn right or left
            ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

            // Move the car
            ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

            // Turn left
            ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
        }
    }
}
/**
 * @brief Determine whether to transition from non-grass to grass
 *
 * @param fusion_result
 * @return true
 * @return false
 */
bool NavigationCrossRegionAlg::Nongrass2Grass(const PerceptionFusionResult &fusion_result)
{
    LOG_INFO_THROTTLE(1000, "[CrossRegion] [Nongrass2Grass1] Processing transition from non-grass to grass");

    // Add the new state to the queue
    frames_.push_back(fusion_result.grass_detecte_status);

    // If the queue is full (exceeds 10 frames), remove the earliest frame
    if (frames_.size() >= 11)
    {
        frames_.pop_front();
    }

    /**
     * NO_GRASS = 0,                // No grass (all obstacles)
     * HAVE_GRASS_NO_OBSTACLE = 1,  // Grass without obstacles (all grass)
     * HAVE_GRASS_HAVE_OBSTACLE = 2 // Grass with obstacles (partial grass and partial obstacles)
     */

    if (frames_.size() >= 10)
    {
        size_t grass_count = 0; // Grass count
        for (const auto &status : frames_)
        {
            if (status == GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE || status == GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE) // Grass without obstacles (all grass)
            {
                grass_count++; // Grass
            }
        }
        LOG_INFO("[CrossRegion] [Nongrass2Grass1] Grass count grass_count({})", grass_count);
        if (int(grass_count) > grass_count_threshold_) // Determine if it is grass
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    // Less than 10 frames, cannot determine
    return false;
}

/**
 * @brief Determine whether to transition from grass to non-grass
 *
 * @param fusion_result
 * @return true
 * @return false
 */
bool NavigationCrossRegionAlg::Grass2Nongrass(const PerceptionFusionResult &fusion_result)
{
    LOG_INFO_THROTTLE(1000, "[CrossRegion] [Grass2Nongrass] Processing transition from grass to non-grass");

    // Add the new state to the queue
    frames_.push_back(fusion_result.grass_detecte_status);

    // If the queue is full (exceeds 10 frames), remove the earliest frame
    if (frames_.size() >= 11)
    {
        frames_.pop_front();
    }

    /**
     * NO_GRASS = 0,                // No grass (all obstacles)
     * HAVE_GRASS_NO_OBSTACLE = 1,  // Grass without obstacles (all grass)
     * HAVE_GRASS_HAVE_OBSTACLE = 2 // Grass with obstacles (partial grass and partial obstacles)
     */

    if (frames_.size() >= 10)
    {
        size_t nongrass_count = 0; // Non-grass count
        for (const auto &status : frames_)
        {
            if (status == GrassDetectStatus::NO_GRASS) // No grass (all obstacles)
            {
                nongrass_count++; // Non-grass
            }
        }
        LOG_INFO("[CrossRegion] [Grass2Nongrass] Non-grass count nongrass_count({})", nongrass_count);
        if (int(nongrass_count) > grass_count_threshold_) // Determine if it is non-grass
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    // Less than 10 frames, cannot determine
    return false;
}

/**
 * @brief Determine whether the lawn mower's cross-region operation is finished, i.e., whether it has experienced a grass → non-grass → grass transition
 *
 * Explanation:
 * 1. Use a deque frames_ to store the most recent grass detection states. When the queue length exceeds 20 frames, automatically discard the oldest data.
 * 2. Use a voting method with the most recent $$window\_size$$ (e.g., 5) frames to determine whether the current area is grass:
 *    - If $$grass\_count \geq nongrass\_count$$, consider the current area as grass; otherwise, consider it as non-grass.
 * 3. State machine logic:
 *    - If the current state is IN_GRASS, when it is detected that the current area is non-grass, switch to the CROSSING state.
 *    - If the current state is CROSSING, when it is detected that the current area is grass again, it indicates that a cross-region operation has been experienced (grass → non-grass → grass), return true, and reset the state to IN_GRASS.
 *
 * @param fusion_result Perception fusion result, where fusion_result.grass_detecte_status represents the current grass detection state
 * @return true  Indicates that the cross-region operation is finished (i.e., the grass → non-grass → grass transition is completed)
 * @return false Indicates that the cross-region operation is not completed
 */
bool NavigationCrossRegionAlg::CrossRegionFinished(const PerceptionFusionResult &fusion_result)
{
    LOG_INFO_THROTTLE(1000, "[CrossRegion] [CrossRegionFinished] Processing grass → non-grass → grass cross-region determination");

    // Add the current detection state to the queue
    frames_.push_back(fusion_result.grass_detecte_status);

    // When the queue length exceeds 20 frames, remove the oldest frame
    if (frames_.size() > 20)
    {
        frames_.pop_front();
    }

    // Set the window size for determining the current area, e.g., take the most recent 10 frames of data
    const size_t window_size = 10;
    size_t effective_size = std::min(frames_.size(), window_size);
    size_t grass_count = 0;    // $$grass\_count$$: Grass count
    size_t nongrass_count = 0; // Non-grass count

    // Iterate through the most recent effective_size frames of data for voting
    for (size_t i = frames_.size() - effective_size; i < frames_.size(); ++i)
    {
        // If the detection state is grass (including with or without obstacles, both are considered grass)
        if (frames_[i] == GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE || frames_[i] == GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE)
        {
            grass_count++;
        }
        // If the detection state is no grass
        else if (frames_[i] == GrassDetectStatus::NO_GRASS)
        {
            nongrass_count++;
        }
    }

    // Determine the current area based on majority voting: if the grass count is higher, consider the current area as grass
    bool current_is_grass = (grass_count >= nongrass_count);
    LOG_INFO("[CrossRegion] Recent ({}) frames: grass_count = ({}), nongrass_count = ({}), current_is_grass = ({})",
             effective_size, grass_count, nongrass_count, current_is_grass ? "true" : "false");

    // State machine logic implementation:
    // 1. The initial state is IN_GRASS. When it is detected that the current area is non-grass, it indicates that the lawn mower has left the grass area and switches to the CROSSING state.
    // 2. When in the CROSSING state, if it is detected that the current area is grass again, it is considered that the cross-region operation is completed.
    if (current_state_ == RegionState::IN_GRASS)
    {
        if (!current_is_grass)
        {
            current_state_ = RegionState::CROSSING;
            LOG_INFO("[CrossRegion] State transitioned from IN_GRASS to CROSSING");
        }
    }
    else if (current_state_ == RegionState::CROSSING)
    {
        if (current_is_grass)
        {
            // Cross-region completed: experienced grass → non-grass → grass
            LOG_INFO("[CrossRegion] Detected the end of the cross-region operation (non-grass returned to grass)");
            // Reset the state to IN_GRASS for the next detection
            current_state_ = RegionState::IN_GRASS;
            // Clear the queue to prevent historical data from interfering with subsequent judgments
            frames_.clear();
            return true;
        }
    }

    // If the state does not meet the conditions for completing the cross-region operation, return false
    return false;
}

/**
 * @brief From the perspective of PassPoints
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ControlProcessToEndPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = 0.55; // 0.375 Pass point: offset from the beacon in the y-axis direction
    // float yaw_des = M_PI / 4;     // 45 Expected adjustment angle
    // float yaw_compare = M_PI / 6; // 30 degrees (used for comparison, although currently unused)

    // Use a local variable to save the current angle for easy updating
    float current_yaw = yaw_first;
    bool need_adjust = false;
    float target_yaw = current_yaw;

    // Determine whether the direction needs to be adjusted
    // If the current angle is in the range of [0, 30°], the target angle is 45° ($$\pi/4$$)
    if (current_yaw >= 0 && current_yaw <= M_PI / 6)
    {
        target_yaw = M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [150°, 180°], the target angle is 135° ($$3\pi/4$$)
    else if (current_yaw >= 5 * M_PI / 6 && current_yaw <= M_PI)
    {
        target_yaw = 3 * M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [0, -30°], the target angle is -45° ($$-\pi/4$$)
    else if (current_yaw >= -M_PI / 6 && current_yaw < 0)
    {
        target_yaw = -M_PI / 4;
        need_adjust = true;
    }
    // If the current angle is in the range of [-180°, -150°], the target angle is -135° ($$-3\pi/4$$)
    else if (current_yaw <= -5 * M_PI / 6 && current_yaw >= -M_PI)
    {
        target_yaw = -3 * M_PI / 4;
        need_adjust = true;
    }

    // If the direction needs to be adjusted, perform the rotation first
    if (need_adjust)
    {
        LOG_INFO("[CrossRegion] Adjust direction: from {} to {}",
                 Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
        ControlRotaryMotion(target_yaw, current_yaw, cross_region_angular_);
        // Assume that after the rotation is completed, the direction of the car is updated
        current_yaw = target_yaw;
    }

    // Determine the position of the car relative to the beacon based on y_first
    if (y_first <= 0) // On the left side of the beacon
    {
        // Determine whether the car is on the left or right side of the pass point
        if (y_first <= -end_point) // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the left side of the beacon, on the left side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -end_point, B_x, distance); // The position of the car is point A, and the pass point is point B

            // Move to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
        else // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the left side of the beacon, on the right side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -end_point, B_x, distance); // The position of the car is point A, and the pass point is point B

            // Move to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1); // Reverse
        }
    }
    else // On the right side of the beacon
    {
        // Determine whether the car is on the left or right side of the pass point
        if (y_first <= end_point) // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the right side of the beacon, on the left side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, end_point, B_x, distance); // The position of the car is point A, and the pass point is point B

            // Move to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1); // Reverse
        }
        else // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] On the right side of the beacon, on the right side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, end_point, B_x, distance); // The position of the car is point A, and the pass point is point B

            // Move to the pass point
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
    }

    // After moving to the pass point, adjust the direction of the car to 0 degrees
    LOG_INFO("[CrossRegion] Adjust direction to M_PI degrees");
    ControlRotaryMotion(M_PI, current_yaw, cross_region_angular_);
}

/**
 * @brief In the case of counterclockwise edge following. Adjust to end_point + channel_stop_pose_x_ based on the beacon
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 * @note 1. The edge following is counterclockwise 2. The base coordinate system is the beacon
 */
void NavigationCrossRegionAlg::CounterClockwiseAlignEndPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = -channel_width_ / 2;

    // Determine whether the car is on the left or right side of the beacon. Counterclockwise edge following
    if (y_first <= 0) // On the right side of the beacon
    {
        // Determine whether the car is on the left or right side of the channel center
        if (y_first <= end_point) // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the right side of the beacon, on the right side of the pass point, turn left (or right) go straight turn left y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // On the right side of the pass point. Turn right
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the right side of the pass point. Turn left
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // Move to the pass point (0, end_point)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // Turn left
                LOG_INFO("[CrossRegion] [Phase 4] Turn left Rotation angle = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
        }
        else // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the right side of the beacon, on the left side of the pass point, turn left (turn right) go straight turn right y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // On the left side of the pass point. Turn right
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the left side of the pass point. Turn right
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // Move to the pass point (0, end_point)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, -1); // Reverse

                // Turn right
                LOG_INFO("[CrossRegion] [Phase 4] Turn right Rotation angle = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // On the left side of the beacon
    {
        // 1. First, according to the beacon coordinates, drive the car to the right side of its beacon
        if (x_first < -adjust_mode_x_direction_threshold_) // Default adjust_mode_x_direction_threshold_
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the left side of the beacon, on the left side of the pass point, turn right (turn left) go straight turn right straight line x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            if (fabsf(-adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // Turn left
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

                // Move the car to (x_first, y_first - pass_point)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // Turn right
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
            else
            {
                // On the left side of the beacon and the pass point, turn right or left
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);

                // Move the car to (x_first - (-adjust_mode_x_direction_threshold_), y_first)
                ControlLinearMotion(-adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, -1);

                // Turn right
                ControlRotaryMotion(-M_PI / 2, -M_PI, cross_region_angular_);

                // Move the car to (x_first - (-adjust_mode_x_direction_threshold_), y_first - pass_point)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // Turn right
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
        }
        else // Outside the 0.5 range
        {
            LOG_INFO("[CrossRegion] [Phase 4] Beacon left, pass point left, turn right (turn left) go straight x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            // On the left side of the beacon and the pass point, turn right or left
            ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

            // Move the car to (x_first, y_first - pass_point)
            ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

            // Turn right
            ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
        }
    }
}

/**
 * @brief In the case of clockwise edge following. Adjust to end_point + channel_stop_pose_x_ based on the beacon
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ClockwiseAlignEndPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = channel_width_ / 2;
    // Determine whether the car is on the left or right side of the beacon. Clockwise edge following
    if (y_first >= 0) // On the left side of the beacon
    {
        // Determine whether the car is on the left or right side of the channel center
        if (y_first <= end_point) // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] Phase 4! Beacon left, pass point right, turn left (or right) go straight turn left y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // On the right side of the pass point. Turn right
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the right side of the pass point.
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // Move to the pass point (0, end_point)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, -1);
                // Turn left
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
        }
        else // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon left, pass point left, turn left (or right) go straight turn right y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));
            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // On the right side of the pass point. Turn right
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // On the right side of the pass point.
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // Move to the pass point (0, channel_width_ / 2)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);
                // Turn right
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // On the right side of the beacon
    {
        // 1. First, according to the beacon coordinates, drive the car to the left side of its beacon
        if (x_first < -adjust_mode_x_direction_threshold_) // Default adjust_mode_x_direction_threshold_
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon right, pass point right, turn right (turn left) go straight turn left straight line x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));
            if (fabsf(adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // Turn left
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // Move the car to (x_first - adjust_mode_x_direction_threshold_, pass_point - y_first)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // Turn left
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
            else
            {
                // On the right side of the beacon and the pass point, turn right or left
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);

                // Move the car to (x_first - adjust_mode_x_direction_threshold_, y_first)
                ControlLinearMotion(-adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, -1);

                // Turn left
                ControlRotaryMotion(M_PI / 2, M_PI, cross_region_angular_);

                // Move the car to (x_first - adjust_mode_x_direction_threshold_, pass_point - y_first)
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // Turn left
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
        }
        else // Outside the -0.5 range
        {
            LOG_INFO("[CrossRegion] Phase 3! Beacon right, pass point right, turn right (turn left) go straight x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));
            // On the right side of the beacon and the pass point, turn right or left
            ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);
            // Move the car to (x_first, pass_point - y_first)
            ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);
            // Turn left
            ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
        }
    }
}

/**
 * @brief Adjust direction based on perception
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result)
{
    switch (mark_loc_result.mark_perception_direction)
    {
    case -1: // Leftward deviation, turn left
        LOG_INFO("Adjust direction leftward based on perception!");
        PublishVelocity(0, cross_region_special_angular_);
        break;

    case 0: // Centered, go straight
        LOG_INFO("Adjust direction straight based on perception!");
        PublishVelocity(cross_region_special_linear_, 0);
        break;

    case 1: // Rightward deviation, turn right
        LOG_INFO("Adjust direction rightward based on perception!");
        PublishVelocity(0, -cross_region_special_angular_);
        break;

    default:
        LOG_INFO("Error in the mark_perception_direction flag based on perception!");
        PublishVelocity(cross_region_special_linear_, 0);
        break;
    }
}

/**
 * @brief Pair positive integers
 *
 * Rule description:
 * If the input positive integer n is odd, return n+1;
 * If n is even, return n-1.
 * The formula is expressed as follows:
 * $$\text{If } n \% 2 == 1, \text{ then output } n+1$$
 * $$\text{If } n \% 2 == 0, \text{ then output } n-1$$
 *
 * @param n Positive integer
 * @return int Paired value
 */
int NavigationCrossRegionAlg::PairNumber(int n)
{
    // Determine if n is odd
    if (n % 2 == 1)
    {
        // If n is odd, return n+1
        return n + 1;
    }
    else
    {
        // If n is even, return n-1
        return n - 1;
    }
}
// Modified safe movement control function
void NavigationCrossRegionAlg::SafeLinearMotion(float target_dis, float current_dis,
                                                float vel_linear, int reverse,
                                                MarkLocationResult &mark_loc_result)
{
    const float step = 0.005f; // Move 0.5cm each time before checking
    float remaining = fabs(target_dis - current_dis);
    front_beacon_detected_ = false;

    while (remaining > 0 && !emergency_stop_)
    {
        LOG_INFO("[CrossRegion] Safe movement control executing");

        // Check the latest beacon status before each movement
        mark_loc_result = mark_loc_result_;
        CheckFrontBeaconCollision(mark_loc_result);

        if (front_beacon_detected_)
        {
            LOG_WARN("[CrossRegion] Detected a valid front beacon, interrupting straight movement process");
            emergency_stop_ = true;
            non_grass_area_reached_ = true; // Force entry into the non-grass processing flow

            PublishVelocity(0.0, 0.0, 100);
            break;
        }

        // Execute a small segment of movement
        float move_dis = std::min(remaining, step);
        uint64_t t = (move_dis / vel_linear) * 1000;
        PublishVelocity(reverse * vel_linear, 0, t);
        remaining -= move_dis;

        LOG_INFO("[CrossRegion] Movement completed | New remaining distance: {:.2f}m", remaining);
    }
}

// Safe linear motion with non-grass detection
void NavigationCrossRegionAlg::SafeLinearMotionWithNonGrassDetection(float target_dis, float current_dis,
                                                                     float vel_linear, int reverse,
                                                                     MarkLocationResult &mark_loc_result,
                                                                     PerceptionFusionResult &fusion_result,
                                                                     bool &motion_completed)
{
    const float step = 0.005f; // Move 0.5cm each time before checking
    float remaining = fabs(target_dis - current_dis);
    front_beacon_detected_ = false;
    motion_completed = false;

    while (remaining > 0 && !emergency_stop_)
    {
        LOG_INFO("[CrossRegion] Safe movement with non-grass detection executing");

        // Get latest fusion result if callback is available
        PerceptionFusionResult current_fusion_result = fusion_result;

        // Check for non-grass terrain first
        if (Grass2Nongrass(fusion_result))
        {
            LOG_WARN("[CrossRegion] Non-grass terrain detected, stopping immediately");
            PublishVelocity(0.0, 0.0, 100);
            motion_completed = true;
            break;
        }

        // Check the latest beacon status before each movement
        mark_loc_result = mark_loc_result_;
        CheckFrontBeaconCollision(mark_loc_result);

        if (front_beacon_detected_)
        {
            LOG_WARN("[CrossRegion] Detected a valid front beacon, interrupting straight movement process");
            emergency_stop_ = true;
            non_grass_area_reached_ = true; // Force entry into the non-grass processing flow

            PublishVelocity(0.0, 0.0, 100);
            motion_completed = true;
            break;
        }

        // Execute a small segment of movement
        float move_dis = std::min(remaining, step);
        uint64_t t = (move_dis / vel_linear) * 1000;
        PublishVelocity(reverse * vel_linear, 0, t);
        remaining -= move_dis;

        LOG_INFO("[CrossRegion] Movement completed | New remaining distance: {:.2f}m", remaining);
    }

    // If we completed the full distance without detecting non-grass
    if (remaining <= 0)
    {
        motion_completed = true;
        LOG_INFO("[CrossRegion] Completed full distance movement without non-grass detection");
    }
}

// New collision detection logic
void NavigationCrossRegionAlg::CheckFrontBeaconCollision(const MarkLocationResult &mark_loc_result)
{
    // Validity check
    if (mark_loc_result.mark_id_distance.empty())
    {
        LOG_INFO("[CrossRegion] mark_id_distance is empty");
        front_beacon_detected_ = false;
        return;
    }

    // Determine whether the beacon is valid. (mark_id_distance less than 50cm is considered that the current beacon is valid)
    int shortest_dis_inx = -1;
    std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
    // FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);
    int paired_beacon_idx = -1;
    FingVaidBeaconPairIdx(mark_id_distance_vec, shortest_dis_inx, paired_beacon_idx);

    if (shortest_dis_inx == -1) // If the beacon is invalid. Do not operate, continue the previous action
    {
        LOG_INFO("[CrossRegion] mark_id_distance is invalid");
        front_beacon_detected_ = false;
        return;
    }
    else // If the beacon is valid
    {
        LOG_INFO("[CrossRegion] mark_id_distance is valid");
        LOG_INFO("[CrossRegion] The valid beacon is mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

        if (next_paired_beacon_id_ == mark_id_distance_vec[shortest_dis_inx].mark_id)
        {
            LOG_INFO("[CrossRegion] Found the next beacon mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

            // Send the beacon id to the localization for cross-region
            SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);

            if (mark_loc_result.detect_status == 2 && mark_loc_result.mark_perception_direction == 0) // The mower is facing the beacon
            {
                LOG_INFO("[CrossRegion] The beacon can calculate the pose");

                front_beacon_detected_ = true;
                return;
            }
        }
        else
        {
            LOG_ERROR("[CrossRegion] The next pair of beacons is incorrect");
        }
    }

    front_beacon_detected_ = false;
}

// 检查跨区信标碰撞的新函数
void NavigationCrossRegionAlg::CheckCrossRegionBeaconCollision(const PerceptionFusionResult &fusion_result,
                                                               bool &collision_detected,
                                                               float &avoidance_angular)
{
    (void)fusion_result; // 避免未使用参数警告
    collision_detected = false;
    avoidance_angular = 0.0f;

    // 获取原始的PerceptionFusionResult数据
    fescue_msgs__msg__PerceptionFusionResult raw_fusion_result;
    {
        std::lock_guard<std::mutex> lck(raw_fusion_mutex_);
        raw_fusion_result = raw_fusion_result_;
    }

    // 检查bev_grass_region中是否存在跨区信标(87)
    const auto &bev_grass_region = raw_fusion_result.bev_grass_region;
    int height = bev_grass_region.height;
    int width = bev_grass_region.width;
    float resolution = bev_grass_region.resolution;

    if (bev_grass_region.cells_array.empty() || height <= 0 || width <= 0 || resolution <= 0)
    {
        LOG_WARN("[CrossRegion] Invalid BEV grass region data for beacon collision detection");
        return;
    }

    // 检查BEV草地区域中的跨区信标
    bool beacon_found_in_grid = false;
    for (int i = 0; i < height; i++)
    {
        for (int j = 0; j < width; j++)
        {
            int index = i * width + j;
            if (index < static_cast<int>(bev_grass_region.cells_array.size()) &&
                bev_grass_region.cells_array[index] == 87) // 跨区信标
            {
                beacon_found_in_grid = true;
                LOG_INFO("[CrossRegion] Cross-region beacon (87) detected in BEV grid at position ({}, {})", i, j);
                break;
            }
        }
        if (beacon_found_in_grid)
            break;
    }

    if (!beacon_found_in_grid)
    {
        return; // 没有检测到跨区信标
    }

    // 检查object_array中的跨区信标轮廓
    for (const auto &object : raw_fusion_result.object_array)
    {
        if (object.class_id == 87) // 跨区信标
        {
            LOG_INFO("[CrossRegion] Cross-region beacon object detected with class_id 87");

            // 检查轮廓是否与小车可能发生碰撞
            bool will_collide = CheckBeaconContourCollision(object);

            if (will_collide)
            {
                collision_detected = true;

                // 根据信标位置决定避障方向
                if (object.pos_left == 1)
                {
                    // 信标在左侧，向右避障
                    avoidance_angular = -cross_region_angular_; // 负值表示向右转
                    LOG_WARN("[CrossRegion] Beacon on left, avoiding right with angular velocity: {}", avoidance_angular);
                }
                else if (object.pos_right == 1)
                {
                    // 信标在右侧，向左避障
                    avoidance_angular = cross_region_angular_; // 正值表示向左转
                    LOG_WARN("[CrossRegion] Beacon on right, avoiding left with angular velocity: {}", avoidance_angular);
                }
                else if (object.pos_ahead == 1)
                {
                    // 信标在正前方，选择一个方向避障（默认向左）
                    avoidance_angular = cross_region_angular_;
                    LOG_WARN("[CrossRegion] Beacon ahead, avoiding left with angular velocity: {}", avoidance_angular);
                }

                break; // 找到第一个需要避障的信标就退出
            }
        }
    }
}

// 检查信标轮廓是否与小车发生碰撞
bool NavigationCrossRegionAlg::CheckBeaconContourCollision(const fescue_msgs__msg__FusionObjectInfo &object)
{
    // 小车的安全区域参数（可以根据实际小车尺寸调整）
    const float MOWER_WIDTH = 0.6f;   // 小车宽度（米）
    const float MOWER_LENGTH = 0.8f;  // 小车长度（米）
    const float SAFETY_MARGIN = 0.2f; // 安全边距（米）

    // 扩展的安全区域
    const float SAFE_WIDTH = MOWER_WIDTH + SAFETY_MARGIN;
    const float SAFE_LENGTH = MOWER_LENGTH + SAFETY_MARGIN;

    // 小车中心位置（假设在坐标原点）
    const float MOWER_CENTER_X = 0.0f;
    const float MOWER_CENTER_Y = 0.0f;

    // 小车安全区域的边界
    const float LEFT_BOUND = MOWER_CENTER_X - SAFE_WIDTH / 2;
    const float RIGHT_BOUND = MOWER_CENTER_X + SAFE_WIDTH / 2;
    const float FRONT_BOUND = MOWER_CENTER_Y + SAFE_LENGTH / 2;
    const float BACK_BOUND = MOWER_CENTER_Y - SAFE_LENGTH / 2;

    LOG_INFO("[CrossRegion] Checking beacon contour collision, contour points: {}", object.contour_array.size());

    // 检查信标轮廓的每个点是否在小车的安全区域内
    for (const auto &point : object.contour_array)
    {
        float point_x = point.x;
        float point_y = point.y;

        // 检查点是否在小车的安全区域内
        if (point_x >= LEFT_BOUND && point_x <= RIGHT_BOUND &&
            point_y >= BACK_BOUND && point_y <= FRONT_BOUND)
        {
            LOG_WARN("[CrossRegion] Collision detected! Beacon contour point ({:.2f}, {:.2f}) is within mower safety zone",
                     point_x, point_y);
            return true; // 发现碰撞
        }
    }

    // 额外检查：如果信标轮廓形成的边界框与小车安全区域重叠
    if (!object.contour_array.empty())
    {
        float min_x = object.contour_array[0].x;
        float max_x = object.contour_array[0].x;
        float min_y = object.contour_array[0].y;
        float max_y = object.contour_array[0].y;

        // 找到轮廓的边界框
        for (const auto &point : object.contour_array)
        {
            min_x = std::min(min_x, static_cast<float>(point.x));
            max_x = std::max(max_x, static_cast<float>(point.x));
            min_y = std::min(min_y, static_cast<float>(point.y));
            max_y = std::max(max_y, static_cast<float>(point.y));
        }

        // 检查边界框是否与小车安全区域重叠
        bool x_overlap = (min_x <= RIGHT_BOUND) && (max_x >= LEFT_BOUND);
        bool y_overlap = (min_y <= FRONT_BOUND) && (max_y >= BACK_BOUND);

        if (x_overlap && y_overlap)
        {
            LOG_WARN("[CrossRegion] Collision detected! Beacon bounding box overlaps with mower safety zone");
            LOG_WARN("[CrossRegion] Beacon bbox: ({:.2f}, {:.2f}) to ({:.2f}, {:.2f})", min_x, min_y, max_x, max_y);
            LOG_WARN("[CrossRegion] Mower safety zone: ({:.2f}, {:.2f}) to ({:.2f}, {:.2f})",
                     LEFT_BOUND, BACK_BOUND, RIGHT_BOUND, FRONT_BOUND);
            return true; // 发现碰撞
        }
    }

    LOG_INFO("[CrossRegion] No collision detected with beacon contour");
    return false; // 没有碰撞
}

void NavigationCrossRegionAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_cross_region_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation cross_region publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}

void NavigationCrossRegionAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}
void NavigationCrossRegionAlg::PublishVelocityAndInterferenceCorrection(ImuData &imu_data)
{
    // If the IMU processor is available and calibrated, use its data
    if (imu_processor_ && imu_processor_->IsBiasCalibrated())
    {
        // Use IMU processor's filtered data for interference detection and correction
        float current_yaw = imu_processor_->getCurrentYaw();

        // Simplified interference detection logic based on processor's filtered data
        // More complex detection/correction logic can be implemented as needed

        LOG_DEBUG("[CrossRegion] Using IMU processor data: current_yaw={:.2f}°",
                  current_yaw * 180.0f / M_PI);

        // Publish straight velocity (interference correction logic can be implemented here)
        PublishVelocity(cross_region_linear_, 0.0);
        return;
    }

    // Fallback to original IMU logic (for backward compatibility)
    // Calculate time step
    auto current_time = std::chrono::steady_clock::now();
    float dt = 0.0f;
    if (!is_first_imu_)
    {
        dt = std::chrono::duration<float>(current_time - last_imu_time_).count();
    }
    else
    {
        is_first_imu_ = false; // Mark first data as processed
        LOG_INFO("[CrossRegion] Ignore first IMU data, dt = 0");
        last_imu_time_ = current_time;
        last_imu_timestamp_ = imu_data.system_timestamp;
        PublishVelocity(cross_region_linear_, 0.0); // Continue straight
        return;                                     // Skip first processing
    }
    last_imu_time_ = current_time;
    last_imu_timestamp_ = imu_data.system_timestamp;

    // Calibrate bias (at startup)
    if (!is_bias_calibrated_)
    {
        CalibrateImuBias(imu_data);
        PublishVelocity(cross_region_linear_, 0.0); // Continue straight during calibration
        return;
    }

    // Subtract bias and apply threshold filtering
    float angular_velocity_z = imu_data.angular_velocity_z - bias_z_;
    if (std::abs(angular_velocity_z) < bias_threshold_)
    {
        angular_velocity_z = 0.0f; // Filter out small angular velocities
    }

    LOG_WARN("[CrossRegion] Current angular velocity: {:.2f} deg/s [{:.2f} rad/s]", angular_velocity_z * 180.0 / M_PI, angular_velocity_z);

    yaw_current_ += angular_velocity_z * dt;
    LOG_INFO("[CrossRegion] Current heading: {:.2f} deg [{:.2f} rad]", yaw_current_ * 180.0 / M_PI, yaw_current_);

    // Step 1: Detect interference
    if (!is_correcting_)
    {
        if (std::abs(angular_velocity_z) > threshold_angular_velocity_)
        {
            // Step 2: Accumulate angle
            accumulated_angle_ += angular_velocity_z * dt;
            LOG_ERROR("[CrossRegion] Interference detected, accumulated angle: {:.2f} deg",
                      accumulated_angle_ * 180.0 / M_PI);

            if (std::abs(accumulated_angle_) > threshold_accumulated_angle_)
            {
                is_correcting_ = true;
                LOG_ERROR("[CrossRegion] Start correction, accumulated angle: {:.2f} deg",
                          accumulated_angle_ * 180.0 / M_PI);
            }
            else
            {
                // Continue straight
                PublishVelocity(cross_region_linear_, 0.0);
                return;
            }
        }
        else
        {
            accumulated_angle_ = 0.0; // Reset accumulated angle when no interference
            PublishVelocity(cross_region_linear_, 0.0);
            return;
        }
    }

    // Step 3: Correction phase
    if (is_correcting_)
    {
        float angle_to_correct = accumulated_angle_;
        float v_linear = cross_region_linear_;
        float t_correction = 1.0; // Correction time 1 second
        float v_angular = angle_to_correct / t_correction;

        // 3a: Interference still present during correction
        if (std::abs(angular_velocity_z) > threshold_angular_velocity_)
        {
            accumulated_angle_ += angular_velocity_z * dt; // Dynamically update
            angle_to_correct = accumulated_angle_;
            v_angular = angle_to_correct / t_correction;
            LOG_ERROR("[CrossRegion] Interference still present during correction, angle to correct: {:.2f} deg",
                      angle_to_correct * 180.0 / M_PI);
        }
        else
        {
            // 3b: No interference during correction
            LOG_ERROR("[CrossRegion] No interference during correction, angle to correct: {:.2f} deg",
                      angle_to_correct * 180.0 / M_PI);
        }

        // Step 5: Publish both linear and angular velocity
        PublishVelocity(v_linear, v_angular);

        // Step 4: Check if correction is complete
        if (std::abs(yaw_current_ - yaw_target_) < threshold_correction_)
        {
            is_correcting_ = false;
            accumulated_angle_ = 0.0;
            LOG_ERROR("[CrossRegion] Correction complete, heading error: {:.2f} deg",
                      (yaw_current_ - yaw_target_) * 180.0 / M_PI);
            PublishVelocity(cross_region_linear_, 0.0); // Resume straight
        }
    }
}

void NavigationCrossRegionAlg::CalibrateImuBias(const ImuData &imu_data)
{
    if (bias_samples_.size() < bias_sample_count_)
    {
        bias_samples_.push_back(imu_data.angular_velocity_z);
        LOG_INFO("[CrossRegion] Collecting bias samples: {}/{}", bias_samples_.size(), bias_sample_count_);
        return;
    }

    // Calculate average bias
    float sum = 0.0f;
    for (const auto &sample : bias_samples_)
    {
        sum += sample;
    }
    bias_z_ = sum / bias_samples_.size();
    is_bias_calibrated_ = true;
    bias_samples_.clear(); // Clear samples
    LOG_INFO("[CrossRegion] Bias calibration complete, bias_z = {:.6f} rad/s", bias_z_);
}

void NavigationCrossRegionAlg::SetImuData(const ImuData &imu_data)
{
    std::lock_guard<std::mutex> lock(imu_data_mtx_);
    imu_data_ = imu_data;

    if (imu_processor_)
    {
        imu_processor_->SetImuData(imu_data);
    }
}

void NavigationCrossRegionAlg::InitializeImuProcessor()
{
    // Set IMU processor parameters
    imu_processor_param_.filter_alpha = 1.0f;
    imu_processor_param_.angular_velocity_threshold = 0.00f;
    imu_processor_param_.bias_calibration_samples = 300;
    imu_processor_param_.rotation_tolerance = 0.17f; // about 10 degrees
    imu_processor_param_.max_rotation_time = 30.0f;
    imu_processor_param_.backup_distance = 0.3f;     // backup distance 0.3m
    imu_processor_param_.backup_speed = 0.2f;        // backup speed 0.2m/s
    imu_processor_param_.max_backup_attempts = 5;    // max backup attempts 5
    imu_processor_param_.enable_data_logging = true; // enable as needed
    imu_processor_param_.log_file_path = "/userdata/log/cross_region_imu.log";

    // Create IMU processor
    imu_processor_ = std::make_unique<ImuDataProcessor>(imu_processor_param_);

    // Set velocity publish callback
    imu_processor_->SetVelocityCallback([this](float linear, float angular, uint64_t duration_ms) {
        this->PublishVelocity(linear, angular, duration_ms);
    });

    // Set IMU data callback
    imu_processor_->SetImuDataCallback([this](const ImuData &processed_data) {
        // Handle filtered IMU data here
        LOG_DEBUG("[CrossRegion] Received processed IMU data: angular_velocity_z = {:.4f}",
                  processed_data.angular_velocity_z);
    });

    // Initialize processor
    imu_processor_->Initialize();

    LOG_INFO("[CrossRegion] IMU processor initialized successfully");
}

void NavigationCrossRegionAlg::ShutdownImuProcessor()
{
    if (imu_processor_)
    {
        imu_processor_->Shutdown();
        imu_processor_.reset();
        LOG_INFO("[CrossRegion] IMU processor shutdown completed");
    }
}

void NavigationCrossRegionAlg::ControlRotaryMotionWithIMU(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    if (!imu_processor_ || !imu_processor_->IsBiasCalibrated())
    {
        LOG_WARN("[CrossRegion] IMU processor not ready, falling back to time-based rotation");
        ControlRotaryMotion(yaw_des, yaw_first, vel_angular);
        return;
    }

    float target_angle = UnifyAngle(yaw_des - yaw_first);
    float sign = target_angle >= 0.0f ? 1.0f : -1.0f;
    // target_angle = std::abs(target_angle);

    LOG_INFO("[CrossRegion] Starting IMU-based rotation: target={:.3f}°, direction={}",
             target_angle * 180.0f / M_PI, sign > 0 ? "left" : "right");

    // Start IMU closed-loop control (now automatically controls speed via callback)
    imu_processor_->StartRotationControl(target_angle, vel_angular);

    // Wait for rotation to complete
    while (imu_processor_->IsRotationControlActive())
    {
        // Rotation control is now handled automatically by the IMU processor's callback function,
        // including normal rotation, timeout backup, continue rotation after backup, etc.
        std::this_thread::sleep_for(std::chrono::milliseconds(50));

        // // Check if we need to stop (for example, if a stop signal is received)
        // if (mower_running_state_ == MowerRunningState::PAUSE)
        // {
        //     imu_processor_->StopRotationControl();
        //     break;
        // }
    }

    // Ensure all motion is stopped (IMU processor already stops on thread exit, this is extra insurance)
    PublishVelocity(0.0f, 0.0f, 100);

    // Get final result
    // Handle the result here if needed
    LOG_INFO("[CrossRegion] IMU-based rotation completed");
}

void NavigationCrossRegionAlg::ControlProcessToPassPointsWithIMU(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = 0.55; // Pass point: offset from the beacon in the y-axis direction

    // Use a local variable to save the current angle for easy updating
    float current_yaw = yaw_first;
    bool need_adjust = false;
    float target_yaw = current_yaw;

    x_after_adjustment_ = x_first;

    // Position-based movement logic (same as original)
    if (y_first <= 0) // The car is on the right side of the beacon
    {
        // Determine whether the direction needs to be adjusted
        if (current_yaw >= 0 && current_yaw <= M_PI / 6)
        {
            target_yaw = M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw >= 2 * M_PI / 6 && current_yaw <= M_PI)
        {
            target_yaw = M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw < 0)
        {
            target_yaw = M_PI / 4;
            need_adjust = true;
        }

        // If the direction needs to be adjusted, perform the rotation first using IMU
        if (need_adjust)
        {
            LOG_INFO("[CrossRegion] [first] Adjust direction with IMU: from {:.1f}° to {:.1f}°",
                     Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
            ControlRotaryMotionWithIMU(target_yaw, current_yaw, cross_region_angular_);
            current_yaw = target_yaw;
        }

        if (y_first <= -pass_point) // The car is on the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon right, and on the right side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -pass_point, B_x, distance);
            ControlLinearMotionWithIMUThread(distance, 0.0, cross_region_linear_, 1, current_yaw);

            x_after_adjustment_ = B_x;
        }
        else // The car is on the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon right, and on the left side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -pass_point, B_x, distance);
            ControlLinearMotionWithIMUThread(distance, 0.0, cross_region_linear_, -1, current_yaw);

            x_after_adjustment_ = B_x;
        }
    }
    else // The car is on the left side of the beacon
    {
        // Determine whether the direction needs to be adjusted
        if (current_yaw >= 0)
        {
            target_yaw = -M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw >= -M_PI / 6 && current_yaw < 0)
        {
            target_yaw = -M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw <= -2 * M_PI / 6 && current_yaw >= -M_PI)
        {
            target_yaw = -M_PI / 4;
            need_adjust = true;
        }

        // If the direction needs to be adjusted, perform the rotation first using IMU
        if (need_adjust)
        {
            LOG_INFO("[CrossRegion] [first] Adjust direction with IMU: from {:.1f}° to {:.1f}°",
                     Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
            ControlRotaryMotionWithIMU(target_yaw, current_yaw, cross_region_angular_);
            current_yaw = target_yaw;
        }

        if (y_first <= pass_point) // The car is on the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon left, and on the right side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, pass_point, B_x, distance);
            ControlLinearMotionWithIMUThread(distance, 0.0, cross_region_linear_, -1, current_yaw);

            x_after_adjustment_ = B_x;
        }
        else // The car is on the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 3] Beacon left, and on the left side of the pass point: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, pass_point, B_x, distance);
            ControlLinearMotionWithIMUThread(distance, 0.0, cross_region_linear_, 1, current_yaw);

            x_after_adjustment_ = B_x;
        }
    }

    // After moving to the pass point, adjust the direction to 0 degrees using IMU
    LOG_INFO("[CrossRegion][second] Adjust direction to 0 degrees with IMU");
    ControlRotaryMotionWithIMU(0.0, current_yaw, cross_region_angular_);
}

void NavigationCrossRegionAlg::ControlProcessToEndPointsWithIMU(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = 0.55; // End point: offset from the beacon in the y-axis direction

    // Use a local variable to save the current angle for easy updating
    float current_yaw = yaw_first;
    bool need_adjust = false;
    float target_yaw = current_yaw;

    // Position-based movement logic (same as original)
    if (y_first <= 0) // On the left side of the beacon
    {
        // Determine whether the direction needs to be adjusted
        if (current_yaw >= 0 && current_yaw <= 4 * M_PI / 6)
        {
            target_yaw = 3 * M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw >= 5 * M_PI / 6 && current_yaw <= M_PI)
        {
            target_yaw = 3 * M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw < 0)
        {
            target_yaw = 3 * M_PI / 4;
            need_adjust = true;
        }

        // If the direction needs to be adjusted, perform the rotation first using IMU
        if (need_adjust)
        {
            LOG_INFO("[CrossRegion][first] Adjust direction with IMU: from {:.1f}° to {:.1f}°",
                     Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
            ControlRotaryMotionWithIMU(target_yaw, current_yaw, cross_region_angular_);
            current_yaw = target_yaw;
        }

        if (y_first <= -end_point) // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the left side of the beacon, on the left side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -end_point, B_x, distance);
            ControlLinearMotionWithIMUThread(distance, 0.0, cross_region_linear_, 1, current_yaw);
        }
        else // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the left side of the beacon, on the right side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -end_point, B_x, distance);
            ControlLinearMotionWithIMUThread(distance, 0.0, cross_region_linear_, -1, current_yaw);
        }
    }
    else // On the right side of the beacon
    {
        // Determine whether the direction needs to be adjusted
        if (current_yaw >= 0)
        {
            target_yaw = -3 * M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw >= -4 * M_PI / 6 && current_yaw < 0)
        {
            target_yaw = -3 * M_PI / 4;
            need_adjust = true;
        }
        else if (current_yaw <= -5 * M_PI / 6 && current_yaw >= -M_PI)
        {
            target_yaw = -3 * M_PI / 4;
            need_adjust = true;
        }

        // If the direction needs to be adjusted, perform the rotation first using IMU
        if (need_adjust)
        {
            LOG_INFO("[CrossRegion][first] Adjust direction with IMU: from {:.1f}° to {:.1f}°",
                     Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
            ControlRotaryMotionWithIMU(target_yaw, current_yaw, cross_region_angular_);
            current_yaw = target_yaw;
        }

        if (y_first <= end_point) // On the left side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the right side of the beacon, on the left side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, end_point, B_x, distance);
            ControlLinearMotionWithIMUThread(distance, 0.0, cross_region_linear_, -1, current_yaw);
        }
        else // On the right side of the pass point
        {
            LOG_INFO("[CrossRegion] [Phase 4] On the right side of the beacon, on the right side of the pass point y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, end_point, B_x, distance);
            ControlLinearMotionWithIMUThread(distance, 0.0, cross_region_linear_, 1, current_yaw);
            // ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
    }

    // After moving to the end point, adjust the direction to 0 degrees using IMU
    LOG_INFO("[CrossRegion][second] Adjust direction to M_PI degrees with IMU");
    ControlRotaryMotionWithIMU(M_PI, current_yaw, cross_region_angular_);
}

//==============================================================================
// MCU Exception handling functions
//==============================================================================
void NavigationCrossRegionAlg::HandleMcuException()
{
    LOG_WARN_THROTTLE(500, "[CrossRegion] Handle collision or lifting exception");

    auto current_time = std::chrono::steady_clock::now();
    auto time_since_last_exception = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_exception_time_).count();
    LOG_INFO("[CrossRegion] Time since last MCU exception: {} seconds", time_since_last_exception);

    // If more than 10 seconds have passed since the last exception handling, reset the retry count
    if (time_since_last_exception > 10)
    {
        mcu_exception_retry_count_ = 0;
        LOG_INFO("[CrossRegion] Reset exception retry count due to time gap");
    }

    if (mcu_exception_retry_count_ < max_mcu_exception_retries_)
    {
        mcu_exception_retry_count_++;
        last_exception_time_ = current_time;

        LOG_WARN("[CrossRegion] MCU exception detected, attempt {} of {}, backing up {:.1f}m",
                 mcu_exception_retry_count_, max_mcu_exception_retries_, mcu_exception_backup_distance_);

        ProcessRecoveryException();
    }
    else
    {
        LOG_ERROR("[CrossRegion] MCU exception retry limit reached ({} attempts), stopping vehicle and reporting error",
                  max_mcu_exception_retries_);

        // Stop the vehicle and publish an exception
        PublishVelocity(0.0f, 0.0f, 100);
        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_MULTIPLE_RECOVERY_MEASURES_EXCEPTION);

        // Reset retry count for the next exception handling
        mcu_exception_retry_count_ = 0;
    }
}

void NavigationCrossRegionAlg::HandleNormalCrossRegionStates()
{
    // When MCU state is normal, reset exception retry count
    mcu_exception_retry_count_ = 0;
}

CrossRegionAlgResult NavigationCrossRegionAlg::HandleNormalOperation(PerceptionFusionResult &fusion_result, MarkLocationResult &mark_loc_result)
{
    (void)fusion_result; // Suppress unused parameter warning when not using BEV phase 4
    float x_first = 0.0;
    float y_first = 0.0;
    float yaw_first = 0.0;
    CrossRegionAlgResult cross_region_result_3(false, CrossRegionStatus::InProgress);
    CrossRegionAlgResult cross_region_result_4(false, CrossRegionStatus::InProgress);

    MotorSpeedData motor_speed_data;
    float act_linear = 0.0f;
    float act_angular = 0.0f;

    {
        std::lock_guard<std::mutex> lock(motor_speed_mtx_);
        motor_speed_data = motor_speed_data_;
        act_linear = act_linear_;
        act_angular = act_angular_;
    }

    (void)act_angular;
    (void)act_linear; // Suppress unused variable warning when not using phase 4

    DealCrossRegionPhase_1(mark_loc_result);
    DealCrossRegionPhase_2(mark_loc_result, x_first, y_first, yaw_first);
    cross_region_result_3 = DealCrossRegionPhase_3(mark_loc_result, x_first, y_first, yaw_first);

#if 1
    DealCrossRegionPhaseWithBEV_4(mark_loc_result, fusion_result, act_linear);

#else
    DealStage4EnteringPassage(x_first, y_first, yaw_first, act_linear);
    cross_region_result_4 = DealCrossRegionPhase_4(mark_loc_result, fusion_result, act_linear);

#endif

    return cross_region_result_3.cross_region_completed ? cross_region_result_3 : cross_region_result_4;
}

CrossRegionAlgResult NavigationCrossRegionAlg::DealCrossRegionPhaseWithBEV_4(MarkLocationResult &mark_loc_result,
                                                                             PerceptionFusionResult &fusion_result,
                                                                             float &act_linear)
{
    bool cross_region_completed = false;

    if (phase_1_completed_ && phase_2_completed_ && phase_3_completed_)
    {
        // DealCrossRegionPhaseWithBEV_41(mark_loc_result, fusion_result, cross_region_completed, act_linear);

        FindBeaconsPhaseWithBEV_41_New(mark_loc_result, fusion_result, cross_region_completed, act_linear);
        UpdateStraightMotionTimingNew(act_linear);
        return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::Successed);
    }

    return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::InProgress);
}

// void NavigationCrossRegionAlg::DealCrossRegionPhaseWithBEV_41(MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result,
//                                                               bool &cross_region_completed, float &act_linear)
// {
//     if (!phase_41_completed_ && !phase_42_completed_ && !phase_43_completed_)
//     {
//         FindBeaconsPhase_41_New(mark_loc_result, fusion_result, cross_region_completed, act_linear);
//     }

//     UpdateStraightMotionTimingNew(act_linear);
// }

void NavigationCrossRegionAlg::FindBeaconsPhaseWithBEV_41_New(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                                                              bool &cross_region_completed,
                                                              float &act_linear)
{
    // Initialize channel passing distance tracking if not started
    if (!channel_distance_tracking_started_)
    {
        channel_distance_tracking_started_ = true;
        channel_accumulated_distance_ = 0.0f;
        channel_distance_start_time_ = std::chrono::steady_clock::now();
        LOG_INFO("[CrossRegion] [Phase 4] Started channel distance tracking, fixed distance: {:.2f}m", channel_fixed_distance_);
    }

    // Update accumulated distance during movement
    if (act_linear > 0.01f) // Only accumulate when actually moving forward
    {
        auto current_time = std::chrono::steady_clock::now();
        if (channel_last_distance_update_time_.time_since_epoch().count() > 0)
        {
            float dt = std::chrono::duration<float>(current_time - channel_last_distance_update_time_).count();
            float delta_distance = act_linear * dt;
            channel_accumulated_distance_ += delta_distance;

            LOG_INFO("[CrossRegion] [Phase 4] Channel distance: {:.2f}m / {:.2f}m",
                     channel_accumulated_distance_, channel_fixed_distance_);
        }
        channel_last_distance_update_time_ = current_time;
    }

    // Check if exceeded fixed distance without beacon detection
    if (channel_accumulated_distance_ >= channel_fixed_distance_)
    {
        LOG_INFO("[CrossRegion] [Phase 4] Exceeded fixed channel distance ({:.2f}m) without beacon detection", channel_fixed_distance_);

        // Check if the current area is grass
        if (Nongrass2Grass(fusion_result))
        {
            LOG_INFO("[CrossRegion] [Phase 4] Current area is grass, starting displacement-based movement with beacon detection");

            bool movement_completed = false;

            // Convert from time-based (3.0s) to distance-based using cross_region_linear_ velocity
            float target_distance = 3.0f * cross_region_linear_; // distance = time * velocity
            TimedMovementWithBEV(target_distance, mark_loc_result, movement_completed, act_linear);

            if (movement_completed)
            {
                StopContinuousLinearMotion();

                LOG_INFO("[CrossRegion] [Phase 4] Displacement-based movement completed, ending cross-region");
                PublishVelocity(0.0, 0.0, 1000); // Stop

                // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
                // ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // Turn right

                ControlLinearMotionWithIMUThread(0.3, 0.0, cross_region_linear_, 1);
                ControlRotaryMotionWithIMU(0.0, 2.35, cross_region_angular_); // Turn right

                cross_region_completed = true;

                {
                    std::lock_guard<std::mutex> lck(cross_region_mutex_);
                    cross_region_state_ = CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION; // Phase 4, non-grass to grass exit cross-region
                    UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION);
                }
                return; // Exit function
            }
        }
        else
        {
            LOG_INFO("[CrossRegion] [Phase 4] Current area is non-grass, continue straight movement");
            PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Go straight with IMU correction
        }
    }
    else
    {
        // Within fixed distance and no beacon detected, continue straight
        LOG_INFO_THROTTLE(1000, "[CrossRegion] [Phase 4] No beacon detected, continue straight through channel");
        PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Go straight with IMU correction
    }
}

void NavigationCrossRegionAlg::TimedMovementWithBEV(float target_distance, const MarkLocationResult &mark_loc_result,
                                                    bool &movement_completed,
                                                    float &act_linear)
{
    (void)mark_loc_result; // 避免未使用参数警告
    movement_completed = false;

    // Initialize displacement tracking if not already started
    if (!timed_movement_in_progress_)
    {
        timed_movement_accumulated_distance_ = 0.0f;
        timed_movement_last_update_time_ = std::chrono::steady_clock::now();
        timed_movement_in_progress_ = true;
        LOG_INFO("[CrossRegion] [TimedMovement] Starting displacement-based movement with beacon detection, target distance: {:.2f}m", target_distance);
    }

    // Update accumulated distance using actual velocity
    auto current_time = std::chrono::steady_clock::now();
    if (timed_movement_last_update_time_.time_since_epoch().count() > 0)
    {
        float dt = std::chrono::duration<float>(current_time - timed_movement_last_update_time_).count();
        float delta_distance = std::abs(act_linear) * dt; // Use absolute value to handle both forward and backward movement
        timed_movement_accumulated_distance_ += delta_distance;

        LOG_INFO_THROTTLE(200, "[CrossRegion] [TimedMovement] Distance progress: {:.3f}m / {:.2f}m",
                          timed_movement_accumulated_distance_, target_distance);
    }
    timed_movement_last_update_time_ = current_time;

    // Check if target displacement is completed
    if (timed_movement_accumulated_distance_ >= target_distance)
    {
        LOG_INFO("[CrossRegion] [TimedMovement] Target distance {:.2f}m completed, continuing straight", target_distance);
        movement_completed = true;
        timed_movement_in_progress_ = false;                          // Reset displacement tracking state
        PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Continue straight movement with IMU correction
        return;
    }

    // Continue moving straight during the displacement period
    PublishVelocityWithIMUCorrection(cross_region_linear_, 0.0f); // Move straight at cross_region_linear_ speed with IMU correction
}

void NavigationCrossRegionAlg::ProcessRecoveryException()
{
    LOG_INFO("[CrossRegion] Executing recovery procedure: backing up {:.1f}m at speed {:.1f}m/s for {}ms",
             mcu_exception_backup_distance_, std::abs(mcu_exception_backup_speed_), mcu_exception_backup_duration_);

    // Backup 0.2m
    PublishVelocity(mcu_exception_backup_speed_, 0.0f, mcu_exception_backup_duration_);

    LOG_INFO("[CrossRegion] Recovery backup completed, continuing current function");
}

// Data validation methods implementation
bool NavigationCrossRegionAlg::CheckPerceptionFusionDataError(const PerceptionFusionResult &fusion_result)
{
    // Check by output time
    uint64_t now_timestamp = std::chrono::steady_clock::now().time_since_epoch().count();
    double fusion_delta_time = (now_timestamp - fusion_result.output_timestamp * 1000000) * 0.000000001; // seconds
    if (fusion_delta_time > 0.5)
    {
        LOG_ERROR_THROTTLE(1000, "[CrossRegion] PerceptionFusion timeout, delta time = {:.3f}s", fusion_delta_time);
        return true;
    }

    // Check by recv time
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("PerceptionFusion");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 2000000; // 2 seconds in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("PerceptionFusion", last_time_info, timeout, data_time_info_map_["PerceptionFusion"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationCrossRegionAlg::CheckMarkLocationDataError(const MarkLocationResult &mark_loc_result)
{
    // Check by timestamp
    uint64_t now_timestamp = std::chrono::steady_clock::now().time_since_epoch().count();
    double mark_delta_time = (now_timestamp - mark_loc_result.timestamp * 1000000) * 0.000000001; // seconds
    if (mark_delta_time > 1.0)                                                                    // 1 second timeout for mark location
    {
        LOG_ERROR_THROTTLE(1000, "[CrossRegion] MarkLocation timeout, delta time = {:.3f}s", mark_delta_time);
        return true;
    }

    // Check by recv time
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("MarkLocation");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 3000000; // 3 seconds in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("MarkLocation", last_time_info, timeout, data_time_info_map_["MarkLocation"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationCrossRegionAlg::CheckImuDataError()
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("ImuData");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 2000000; // 2 seconds in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("ImuData", last_time_info, timeout, data_time_info_map_["ImuData"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationCrossRegionAlg::CheckMcuExceptionDataError()
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("McuException");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 1000000; // 1 second in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("McuException", last_time_info, timeout, data_time_info_map_["McuException"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationCrossRegionAlg::CheckMotorSpeedDataError()
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("MotorSpeed");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 2000000; // 2 seconds in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("MotorSpeed", last_time_info, timeout, data_time_info_map_["MotorSpeed"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

void NavigationCrossRegionAlg::CheckTimeout(const std::string &name, const DataTimeInfo &last_time_info,
                                            uint64_t timeout, DataTimeInfo &cur_time_info)
{
    uint64_t now_timestamp = GetSteadyClockTimestampMs() * 1000;
    // Check timeout
    uint64_t delta_recv_time = now_timestamp - last_time_info.recv_timestamp;
    cur_time_info.is_timeout = false;
    if (delta_recv_time > timeout)
    {
        LOG_ERROR_THROTTLE(2000, "[CrossRegion] {} timeout, delta_recv_time: {} us total timeout: {} us",
                           name, delta_recv_time, timeout);
        cur_time_info.is_timeout = true;
    }
}

void NavigationCrossRegionAlg::UpdateDataTimeInfo(const std::string &name, const DataTimeInfo &last_time_info,
                                                  uint64_t low_freq_time, uint32_t low_freq_count_max,
                                                  uint64_t cur_send_timestamp, DataTimeInfo &cur_time_info)
{
    uint64_t now_timestamp = GetSteadyClockTimestampMs() * 1000;
    // Check low frequency
    uint64_t delta_send_time = cur_send_timestamp - last_time_info.send_timestamp;
    if (last_time_info.send_timestamp > 0 && (delta_send_time == 0 || delta_send_time > low_freq_time))
    {
        cur_time_info.low_freq_count++;
    }
    else
    {
        cur_time_info.low_freq_count = 0;
    }
    cur_time_info.is_low_freq = false;
    if (cur_time_info.low_freq_count > low_freq_count_max)
    {
        LOG_ERROR("[CrossRegion] {} low freq, cur delta_send_time: {} us", name, delta_send_time);
        cur_time_info.is_low_freq = true;
    }
    cur_time_info.recv_timestamp = now_timestamp;
    cur_time_info.send_timestamp = cur_send_timestamp;
}

void NavigationCrossRegionAlg::SetPerceptionFusionResult(const PerceptionFusionResult &fusion_result)
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("PerceptionFusion");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    UpdateDataTimeInfo("PerceptionFusion", last_time_info, low_freq_time, low_freq_count_max,
                       fusion_result.output_timestamp, data_time_info_map_["PerceptionFusion"]);
}

void NavigationCrossRegionAlg::SetMarkLocationResultWithTimeInfo(const MarkLocationResult &mark_loc_result)
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("MarkLocation");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    UpdateDataTimeInfo("MarkLocation", last_time_info, low_freq_time, low_freq_count_max,
                       mark_loc_result.timestamp, data_time_info_map_["MarkLocation"]);
}

void NavigationCrossRegionAlg::SetImuDataWithTimeInfo(const ImuData &imu_data)
{
    (void)imu_data; // Suppress unused parameter warning
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("ImuData");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    // Use current timestamp as IMU data might not have a timestamp field
    uint64_t current_timestamp = GetSteadyClockTimestampMs();
    UpdateDataTimeInfo("ImuData", last_time_info, low_freq_time, low_freq_count_max,
                       current_timestamp, data_time_info_map_["ImuData"]);
}

void NavigationCrossRegionAlg::SetMcuExceptionWithTimeInfo(const McuExceptionStatus &mcu_exception_status)
{
    (void)mcu_exception_status; // Suppress unused parameter warning
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("McuException");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    // Use current timestamp as MCU exception status might not have a timestamp field
    uint64_t current_timestamp = GetSteadyClockTimestampMs();
    UpdateDataTimeInfo("McuException", last_time_info, low_freq_time, low_freq_count_max,
                       current_timestamp, data_time_info_map_["McuException"]);
}

void NavigationCrossRegionAlg::SetMotorSpeedDataWithTimeInfo(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("MotorSpeed");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    UpdateDataTimeInfo("MotorSpeed", last_time_info, low_freq_time, low_freq_count_max,
                       motor_speed_data.system_timestamp, data_time_info_map_["MotorSpeed"]);
}

} // namespace fescue_iox
