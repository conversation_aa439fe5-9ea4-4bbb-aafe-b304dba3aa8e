#include "cross_region.hpp"
#include "utils/logger.hpp"

namespace fescue_iox
{

// 通用的区域障碍物检测函数
// region_type: 0-全部区域, 1-左侧区域, 2-右侧区域, 3-中心区域, 4-左半区域, 5-右半区域
// threshold: 障碍物占比阈值，超过此阈值返回true
bool NavigationCrossRegionAlg::DetectObstacleInRegion(const std::vector<std::vector<uint8_t>> &grid, int height, int width,
                                                      int region_type, float threshold)
{
    // 检查参数有效性
    if (grid.empty() || height <= 0 || width <= 0)
    {
        LOG_ERROR("[CrossRegion] Invalid grid parameters in DetectObstacleInRegion");
        return false;
    }

    // 定义区域边界
    int left_boundary = 0;
    int right_boundary = width;
    int top_boundary = 0;
    int bottom_boundary = height;

    // 根据区域类型设置边界
    switch (region_type)
    {
    case 1: // 左侧区域 (左1/3)
        right_boundary = width / 3;
        break;
    case 2: // 右侧区域 (右1/3)
        left_boundary = width * 2 / 3;
        break;
    case 3: // 中心区域 (中间1/3)
        left_boundary = width / 3;
        right_boundary = width * 2 / 3;
        break;
    case 4: // 左半区域 (左1/2)
        right_boundary = width / 2;
        break;
    case 5: // 右半区域 (右1/2)
        left_boundary = width / 2;
        break;

    case 6: // 中心区域 (中间1/3)
        left_boundary = width / 3;
        right_boundary = width * 2 / 3;
        // top_boundary = height * 1 / 2;
        top_boundary = height * 2 / 3;
        bottom_boundary = height;
        break;
    default: // 全部区域
        break;
    }

    // 计算区域单元格总数和障碍物单元格数
    int total_cells = (right_boundary - left_boundary) * (bottom_boundary - top_boundary);
    int obstacle_cells = 0;

    // 遍历区域，计算障碍物数量
    for (int y = top_boundary; y < bottom_boundary; y++)
    {
        if (static_cast<int>(grid[y].size()) != width)
        {
            LOG_ERROR("[CrossRegion] Inconsistent grid width at row {}: expected {}, got {}",
                      y, width, grid[y].size());
            return false;
        }

        for (int x = left_boundary; x < right_boundary; x++)
        {
            if (grid[y][x] == 1) // 如果是障碍物
            {
                obstacle_cells++;
            }
        }
    }

    // 计算障碍物占比
    float obstacle_ratio = static_cast<float>(obstacle_cells) / total_cells;

    // 记录日志
    LOG_INFO_THROTTLE(5000, "[CrossRegion] Region {} obstacle ratio: {:.2f}%, threshold: {:.2f}%",
                      region_type, obstacle_ratio * 100, threshold * 100);

    // 如果障碍物占比超过阈值，返回true
    return (obstacle_ratio > threshold);
}

// 检查左侧是否有障碍物
bool NavigationCrossRegionAlg::HasLeftObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // 使用通用函数检测左侧区域障碍物，阈值设为0.01（1%）
    return DetectObstacleInRegion(grid, height, width, 1, threshold);
}

// 检查右侧是否有障碍物
bool NavigationCrossRegionAlg::HasRightObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // 使用通用函数检测右侧区域障碍物，阈值设为0.01（1%）
    return DetectObstacleInRegion(grid, height, width, 2, threshold);
}

// 检查左侧是否超过一半面积是障碍物
bool NavigationCrossRegionAlg::HasLeftHalfObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // 使用通用函数检测左侧区域障碍物
    return DetectObstacleInRegion(grid, height, width, 4, threshold);
}

// 检查右侧是否超过一半面积是障碍物
bool NavigationCrossRegionAlg::HasRightHalfObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // 使用通用函数检测右侧区域障碍物
    return DetectObstacleInRegion(grid, height, width, 5, threshold);
}

// 检查是否全是草地（无障碍物）
bool NavigationCrossRegionAlg::IsAllGrass(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // 使用通用函数检测全部区域障碍物，阈值设为0.01（1%）
    // 注意这里返回的是相反的结果，因为IsAllGrass是检查是否没有障碍物
    return !DetectObstacleInRegion(grid, height, width, 0, threshold);
}

// 检查左侧是否超过一半面积是草地
bool NavigationCrossRegionAlg::HasLeftHalfGrass(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // 使用通用函数检测全部区域障碍物，阈值设为0.01（1%）
    // 注意这里返回的是相反的结果，因为HasLeftHalfGrass是检查是否没有障碍物
    return !DetectObstacleInRegion(grid, height, width, 4, threshold);
}

// 检查是否全是障碍物
bool NavigationCrossRegionAlg::IsAllObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    // 使用通用函数检测全部区域障碍物，阈值设为0.01（1%）
    return DetectObstacleInRegion(grid, height, width, 0, threshold);
}

// 检查前方是否有障碍物，并返回障碍物距离
bool NavigationCrossRegionAlg::HasForwardObstacle(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float threshold)
{
    return DetectObstacleInRegion(grid, height, width, 6, threshold);
}

// 检测特定区域的障碍物分布
// 返回一个包含障碍物数量、总单元格数量和障碍物占比的结构体
ObstacleDistribution NavigationCrossRegionAlg::GetObstacleDistribution(const std::vector<std::vector<uint8_t>> &grid,
                                                                       int height, int width)
{
    ObstacleDistribution distribution;

    // 检查参数有效性
    if (grid.empty() || height <= 0 || width <= 0)
    {
        LOG_ERROR("[CrossRegion] Invalid grid parameters in GetObstacleDistribution");
        return distribution;
    }

    // 定义区域边界
    int center_x = width / 2;
    (void)center_x; // 避免未使用变量警告
    int left_boundary = width / 3;
    int right_boundary = width * 2 / 3;

    // 初始化计数器
    distribution.left_obstacles = 0;
    distribution.center_obstacles = 0;
    distribution.right_obstacles = 0;
    distribution.left_cells = 0;
    distribution.center_cells = 0;
    distribution.right_cells = 0;

    // 遍历栅格图，统计各区域障碍物
    for (int y = 0; y < height; y++)
    {
        if (static_cast<int>(grid[y].size()) != width)
        {
            LOG_ERROR("[CrossRegion] Inconsistent grid width at row {}: expected {}, got {}",
                      y, width, grid[y].size());
            return distribution;
        }

        for (int x = 0; x < width; x++)
        {
            // 确定区域
            if (x < left_boundary)
            {
                distribution.left_cells++;
                if (grid[y][x] == 1)
                {
                    distribution.left_obstacles++;
                }
            }
            else if (x >= right_boundary)
            {
                distribution.right_cells++;
                if (grid[y][x] == 1)
                {
                    distribution.right_obstacles++;
                }
            }
            else
            {
                distribution.center_cells++;
                if (grid[y][x] == 1)
                {
                    distribution.center_obstacles++;
                }
            }
        }
    }

    // 计算各区域障碍物占比
    distribution.left_ratio = distribution.left_cells > 0 ? static_cast<float>(distribution.left_obstacles) / distribution.left_cells : 0.0f;

    distribution.center_ratio = distribution.center_cells > 0 ? static_cast<float>(distribution.center_obstacles) / distribution.center_cells : 0.0f;

    distribution.right_ratio = distribution.right_cells > 0 ? static_cast<float>(distribution.right_obstacles) / distribution.right_cells : 0.0f;

    // 计算总体障碍物占比
    int total_cells = distribution.left_cells + distribution.center_cells + distribution.right_cells;
    int total_obstacles = distribution.left_obstacles + distribution.center_obstacles + distribution.right_obstacles;

    distribution.total_ratio = total_cells > 0 ? static_cast<float>(total_obstacles) / total_cells : 0.0f;

    return distribution;
}

// 综合检测所有障碍物信息 - 优化函数，一次性获取所有障碍物信息
ObstacleInfo NavigationCrossRegionAlg::DetectAllObstacles(const std::vector<std::vector<uint8_t>> &grid, int height, int width, float resolution)
{
    ObstacleInfo info;

    // 检查参数有效性
    if (grid.empty() || height <= 0 || width <= 0 || resolution <= 0)
    {
        LOG_ERROR_THROTTLE(1000, "[CrossRegion] Invalid parameters in DetectAllObstacles: height={}, width={}, resolution={}",
                           height, width, resolution);
        return info;
    }

    // 1. 获取障碍物分布信息
    info.distribution = GetObstacleDistribution(grid, height, width);

    // 2. 检测各区域障碍物情况
    info.is_all_grass = !DetectObstacleInRegion(grid, height, width, 0, 0.01f);
    info.has_left_obstacle = DetectObstacleInRegion(grid, height, width, 1, 0.01f);
    info.has_right_obstacle = DetectObstacleInRegion(grid, height, width, 2, 0.01f);
    info.has_left_half_obstacle = DetectObstacleInRegion(grid, height, width, 1, obstacle_ratio_threshold_);
    info.has_right_half_obstacle = DetectObstacleInRegion(grid, height, width, 2, obstacle_ratio_threshold_);

    LOG_DEBUG_THROTTLE(5000, "Obstacle detection: is_all_grass={%d}, "
                             "left_obstacle={%d}, right_obstacle={%d}, left_half={%d}, right_half={%d}",
                       info.is_all_grass,
                       info.has_left_obstacle, info.has_right_obstacle,
                       info.has_left_half_obstacle, info.has_right_half_obstacle);

    return info;
}

} // namespace fescue_iox
